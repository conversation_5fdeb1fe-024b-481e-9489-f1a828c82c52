"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisModule = void 0;
const common_1 = require("@nestjs/common");
const ioredis_1 = require("ioredis");
const redis_service_1 = require("./redis.service");
const common_2 = require("@nestjs/common");
/**
 * Global Redis Module that provides centralized Redis connections for the entire application.
 * Uses ioredis with cluster support for ElastiCache compatibility.
 */
let RedisModule = class RedisModule {
};
exports.RedisModule = RedisModule;
exports.RedisModule = RedisModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        providers: [
            redis_service_1.RedisService,
            {
                provide: 'REDIS_CLIENT',
                useFactory: async () => {
                    return createIoRedisClientInstance('CLIENT');
                }
            },
            {
                provide: 'REDIS_PUB_CLIENT',
                useFactory: async () => {
                    return createIoRedisClientInstance('PUB');
                }
            },
            {
                provide: 'REDIS_SUB_CLIENT',
                useFactory: async () => {
                    return createIoRedisClientInstance('SUB');
                }
            }
        ],
        exports: [
            redis_service_1.RedisService,
            'REDIS_CLIENT',
            'REDIS_PUB_CLIENT',
            'REDIS_SUB_CLIENT'
        ]
    })
], RedisModule);
/**
 * Helper function to generate effective ioredis configuration based on environment variables
 * Supports both REDIS_URL and individual parameters, with cluster detection
 */
function getEffectiveIoRedisConfig() {
    const logger = new common_2.Logger('RedisModule-Config');
    // Handle both REDIS_URL and REDIS_HOST configurations
    let REDIS_CONNECTION_STRING;
    if (process.env.REDIS_URL) {
        // For ElastiCache with TLS, should use rediss:// scheme
        REDIS_CONNECTION_STRING = process.env.REDIS_URL;
        logger.log(`Using REDIS_URL configuration: ${maskRedisUrl(REDIS_CONNECTION_STRING)}`);
    }
    else {
        REDIS_CONNECTION_STRING = process.env.REDIS_HOST || 'localhost:6379';
        logger.log(`Using REDIS_HOST configuration: ${REDIS_CONNECTION_STRING}`);
    }
    const REDIS_TLS = process.env.REDIS_TLS === 'true';
    const REDIS_PASSWORD = process.env.REDIS_PASSWORD;
    logger.log(`Redis TLS: ${REDIS_TLS ? 'enabled' : 'disabled'}`);
    logger.log(`Redis Password: ${REDIS_PASSWORD ? '***configured***' : 'none'}`);
    // Parse startup nodes for cluster
    const getStartupNodes = (connectionString) => {
        if (connectionString.startsWith('redis://') ||
            connectionString.startsWith('rediss://')) {
            // Single URL (like ElastiCache config endpoint) - ioredis.Cluster accepts URL strings
            return [connectionString];
        }
        // Comma-separated list of host:port for REDIS_HOST
        return connectionString.split(',').map(hostPort => {
            const [hostname, portStr] = hostPort.trim().split(':');
            return { host: hostname, port: parseInt(portStr) || 6379 };
        });
    };
    const startupNodes = getStartupNodes(REDIS_CONNECTION_STRING);
    // Determine if this is a cluster or single node
    const isCluster = startupNodes.length > 1 ||
        REDIS_CONNECTION_STRING.includes('clustercfg') ||
        REDIS_CONNECTION_STRING.includes('cluster');
    logger.log(`Parsed startup nodes:`, startupNodes);
    logger.log(`Detected configuration: ${isCluster ? 'CLUSTER' : 'SINGLE NODE'}`);
    if (isCluster) {
        // Cluster configuration based on proven patterns from implementation summary
        const clusterOptions = {
            // Cluster-level options
            enableOfflineQueue: true, // Recommended 'true' for production
            retryDelayOnFailover: 100,
            enableReadyCheck: true,
            lazyConnect: false, // Connect immediately to catch issues at startup
            enableAutoPipelining: false,
            // Optional: Explicit retry strategy for when all nodes are down
            clusterRetryStrategy: (times) => {
                const delay = Math.min(times * 100, 3000); // 100ms, 200ms, ..., up to 3s
                logger.warn(`Cluster retry attempt ${times}, delaying for ${delay}ms`);
                return delay;
            },
            // Per-node Redis options (CRITICAL: Must be nested in redisOptions)
            redisOptions: {
                maxRetriesPerRequest: 3, // Retries for individual node connections
                lazyConnect: false, // Match cluster-level setting
                ...(REDIS_TLS && {
                    tls: {
                        rejectUnauthorized: false // Required for ElastiCache default certificates
                    }
                }),
                // Note: password should be undefined for current ElastiCache (no auth)
                ...(REDIS_PASSWORD && {
                    password: REDIS_PASSWORD
                })
            }
        };
        return {
            isCluster: true,
            startupNodes,
            clusterOptions
        };
    }
    else {
        // Single node configuration
        const singleNodeOptions = {
            maxRetriesPerRequest: 10,
            lazyConnect: false,
            ...(REDIS_TLS && {
                tls: {
                    rejectUnauthorized: false
                }
            }),
            ...(REDIS_PASSWORD && {
                password: REDIS_PASSWORD
            })
        };
        return {
            isCluster: false,
            startupNodes,
            singleNodeOptions
        };
    }
}
/**
 * Factory function to create ioredis client instances
 * Handles both cluster and single node configurations
 */
async function createIoRedisClientInstance(clientType) {
    const logger = new common_2.Logger(`RedisModule-${clientType}-Factory`);
    const config = getEffectiveIoRedisConfig();
    let client;
    try {
        if (config.isCluster) {
            logger.log(`Creating ioredis.Cluster for ${clientType}...`);
            client = new ioredis_1.Cluster(config.startupNodes, config.clusterOptions);
        }
        else {
            logger.log(`Creating ioredis.Redis for ${clientType}...`);
            // For single node, use the first (and only) startup node
            const nodeConfig = config.startupNodes[0];
            if (typeof nodeConfig === 'string') {
                // URL format
                client = new ioredis_1.Redis(nodeConfig, config.singleNodeOptions);
            }
            else {
                // Object format
                client = new ioredis_1.Redis(nodeConfig.port, nodeConfig.host, config.singleNodeOptions);
            }
        }
        // Comprehensive event listeners
        client.on('connect', () => {
            logger.log(`ioredis ${clientType} client connected successfully`);
        });
        client.on('ready', () => {
            logger.log(`ioredis ${clientType} client is ready`);
        });
        client.on('error', err => {
            logger.error(`ioredis ${clientType} client error:`, err);
        });
        client.on('close', () => {
            logger.warn(`ioredis ${clientType} client connection closed`);
        });
        client.on('reconnecting', () => {
            logger.log(`ioredis ${clientType} client reconnecting...`);
        });
        // Cluster-specific events
        if (config.isCluster) {
            const clusterClient = client;
            clusterClient.on('+node', node => {
                logger.log(`Cluster node added: ${node.options.host}:${node.options.port}`);
            });
            clusterClient.on('-node', node => {
                logger.warn(`Cluster node removed: ${node.options.host}:${node.options.port}`);
            });
            clusterClient.on('node error', (err, node) => {
                logger.error(`Cluster node error on ${node.options.host}:${node.options.port}:`, err);
            });
        }
        // Note: No explicit connect() needed since lazyConnect: false
        // ioredis will auto-connect when lazyConnect is false
        return client;
    }
    catch (err) {
        logger.error(`Error creating ${clientType} Redis client:`, err);
        throw err;
    }
}
/**
 * Mask Redis URL to hide credentials in logs
 */
function maskRedisUrl(url) {
    try {
        const parsedUrl = new URL(url);
        if (parsedUrl.password) {
            parsedUrl.password = '****';
        }
        return parsedUrl.toString();
    }
    catch (e) {
        return 'Invalid Redis URL';
    }
}
//# sourceMappingURL=redis.module.js.map