"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicSettingsResponseDto = exports.ClinicSettingsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class ClinicSettingsDto {
}
exports.ClinicSettingsDto = ClinicSettingsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether patient last name should match owner last name by default',
        example: true,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ClinicSettingsDto.prototype, "patientLastNameAsOwnerLastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Default filter for patient list display',
        example: 'alive',
        enum: ['all', 'alive'],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['all', 'alive']),
    __metadata("design:type", String)
], ClinicSettingsDto.prototype, "defaultPatientList", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Default filter for appointment booking patient list',
        example: 'alive',
        enum: ['all', 'alive'],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['all', 'alive']),
    __metadata("design:type", String)
], ClinicSettingsDto.prototype, "appointmentBookingList", void 0);
class ClinicSettingsResponseDto {
}
exports.ClinicSettingsResponseDto = ClinicSettingsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether patient last name should match owner last name by default',
        example: true
    }),
    __metadata("design:type", Boolean)
], ClinicSettingsResponseDto.prototype, "patientLastNameAsOwnerLastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Default filter for patient list display',
        example: 'alive',
        enum: ['all', 'alive']
    }),
    __metadata("design:type", String)
], ClinicSettingsResponseDto.prototype, "defaultPatientList", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Default filter for appointment booking patient list',
        example: 'alive',
        enum: ['all', 'alive']
    }),
    __metadata("design:type", String)
], ClinicSettingsResponseDto.prototype, "appointmentBookingList", void 0);
//# sourceMappingURL=clinic-settings.dto.js.map