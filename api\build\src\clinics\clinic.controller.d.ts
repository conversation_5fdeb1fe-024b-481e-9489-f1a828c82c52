import { ClinicService } from './clinic.service';
import { CreateClinicDto, UpdateBasicClinicDto } from './dto/create-clinic.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { ClinicEntity } from './entities/clinic.entity';
import { ClinicRoomEntity } from './entities/clinic-room.entity';
import { UpdateClinicDto } from './dto/update-clinic.dto';
import { Request, Response } from 'express';
import { CreateClinicRoomDto, UpdateClinicRoomDto } from './dto/create-clinic-room.dto';
import { UpdateClientBookingSettingsDto } from './dto/update-client-booking-settings.dto';
import { ClientBookingSettingsResponseDto } from './dto/client-booking-settings-response.dto';
import { ClinicSettingsDto, ClinicSettingsResponseDto } from './dto/clinic-settings.dto';
export declare class ClinicController extends ApiDocumentationBase {
    private readonly logger;
    private readonly clinicService;
    constructor(logger: <PERSON>Logger, clinicService: ClinicService);
    createClinic(createClinicDto: CreateClinicDto, req: Request): Promise<ClinicEntity>;
    getClinicById(id: string): Promise<ClinicEntity>;
    updateBasicClinic(id: string, updateBasicClinicDto: UpdateBasicClinicDto, req: Request): Promise<ClinicEntity>;
    deactivateClinic(id: string): Promise<ClinicEntity>;
    reactivateClinic(id: string): Promise<ClinicEntity>;
    softDeleteClinic(id: string): Promise<ClinicEntity>;
    updateClinic(id: string, updateClinicDto: UpdateClinicDto, req: Request): Promise<ClinicEntity>;
    getAllClinics(page?: number, limit?: number, orderBy?: string): Promise<{
        clinics: ClinicEntity[];
        total: number;
    }>;
    getClinicRooms(id: string): Promise<{
        rooms: ClinicRoomEntity[];
        total: number;
    }>;
    createClinicRoom(createClinicRoomDto: CreateClinicRoomDto, req: {
        user: {
            clinicId: string;
            brandId: string;
        };
    }): Promise<ClinicRoomEntity>;
    updateClinicRoom(id: string, updateClinicRoomDto: UpdateClinicRoomDto): Promise<ClinicRoomEntity>;
    deleteRoom(id: string): Promise<{
        message: string;
    }>;
    uploadFile(file: Express.Multer.File, clinicId: string, brandId: string, request: Request): Promise<{
        [sheetName: string]: any;
    }>;
    downloadInventory(clinicId: string, res: Response): Promise<void>;
    deleteInventoryItem(itemType: string, itemId: string): Promise<{
        message: string;
    }>;
    getClientBookingSettings(id: string): Promise<ClientBookingSettingsResponseDto | null>;
    updateClientBookingSettings(id: string, dto: UpdateClientBookingSettingsDto, // Use the new DTO
    req: Request): Promise<ClinicEntity>;
    getClinicSettings(id: string): Promise<ClinicSettingsResponseDto>;
    updateClinicSettings(id: string, dto: ClinicSettingsDto, req: Request): Promise<ClinicSettingsResponseDto>;
}
