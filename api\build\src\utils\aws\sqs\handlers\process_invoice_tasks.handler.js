"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessInvoiceTasksHandler = void 0;
const common_1 = require("@nestjs/common");
const winston_logger_service_1 = require("../../../logger/winston-logger.service");
const s3_service_1 = require("../../s3/s3.service");
const send_mail_service_1 = require("../../ses/send-mail-service");
const whatsapp_service_1 = require("../../../whatsapp-integration/whatsapp.service");
const typeorm_1 = require("@nestjs/typeorm");
const patient_vaccinations_entity_1 = require("../../../../patient-vaccinations/entities/patient-vaccinations.entity");
const typeorm_2 = require("typeorm");
const patient_entity_1 = require("../../../../patients/entities/patient.entity");
const patient_reminder_service_1 = require("../../../../patient-reminders/patient-reminder.service");
const global_reminders_service_1 = require("../../../../patient-global-reminders/global-reminders.service");
const generatePdf_1 = require("../../../generatePdf");
const generatePrescription_1 = require("../../../pdfs/new/generatePrescription");
const generateVaccinationCertificate_1 = require("../../../pdfs/new/generateVaccinationCertificate");
const moment = require("moment");
const uuidv7_1 = require("uuidv7");
const enum_plan_type_1 = require("../../../../clinic-plans/enums/enum-plan-type");
const clinic_product_entity_1 = require("../../../../clinic-products/entities/clinic-product.entity");
const clinic_vaccination_entity_1 = require("../../../../clinic-vaccinations/entities/clinic-vaccination.entity");
const clinic_medication_entity_1 = require("../../../../clinic-medications/entities/clinic-medication.entity");
const clinic_consumable_entity_1 = require("../../../../clinic-consumables/entities/clinic-consumable.entity");
const mail_template_generator_1 = require("../../../mail-generator/mail-template-generator");
const generate_alpha_numeric_code_1 = require("../../../common/generate_alpha-numeric_code");
const whatsapp_template_generator_1 = require("../../../communicatoins/whatsapp-template-generator");
const get_login_url_1 = require("../../../common/get-login-url");
const constants_1 = require("../../../constants");
const axios_1 = require("axios");
const invoice_entity_1 = require("../../../../invoice/entities/invoice.entity");
const payment_details_entity_1 = require("../../../../payment-details/entities/payment-details.entity");
const enum_invoice_types_1 = require("../../../../invoice/enums/enum-invoice-types");
const enum_credit_types_1 = require("../../../../payment-details/enums/enum-credit-types");
const generateInvoice_1 = require("../../../pdfs/new/generateInvoice");
const generateCreditNote_1 = require("../../../pdfs/new/generateCreditNote");
const whatsapp_template_generator_2 = require("../../../communicatoins/whatsapp-template-generator");
const generatePaymentReceipt_1 = require("../../../pdfs/new/generatePaymentReceipt");
const generateRefundReceipt_1 = require("../../../pdfs/new/generateRefundReceipt");
const whatsapp_template_generator_3 = require("../../../communicatoins/whatsapp-template-generator");
const clinic_entity_1 = require("../../../../clinics/entities/clinic.entity");
const owner_brand_entity_1 = require("../../../../owners/entities/owner-brand.entity");
const patients_service_1 = require("../../../../patients/patients.service");
const clinic_lab_report_service_1 = require("../../../../clinic-lab-report/clinic-lab-report.service");
const template_helper_util_1 = require("../../../common/template-helper.util");
const appointment_details_entity_1 = require("../../../../appointments/entities/appointment-details.entity");
const appointment_doctor_entity_1 = require("../../../../appointments/entities/appointment-doctor.entity");
// Enum for payment types if not already defined
var EnumPaymentType;
(function (EnumPaymentType) {
    EnumPaymentType["Cash"] = "Cash";
    EnumPaymentType["Card"] = "Card";
    EnumPaymentType["Credits"] = "Credits";
})(EnumPaymentType || (EnumPaymentType = {}));
let ProcessInvoiceTasksHandler = class ProcessInvoiceTasksHandler {
    constructor(logger, s3Service, mailService, whatsappService, patientVaccinationRepository, patientRepository, clinicProduct, clinicVaccination, clinicMedication, clinicConsumableEntity, invoiceRepository, paymentDetailsRepository, clinicRepository, ownerBrandRepository, patientRemindersService, globalReminderService, patientService, clinicLabReportService, appointmentDetailsEntity, appointmentDoctorsEntity) {
        this.logger = logger;
        this.s3Service = s3Service;
        this.mailService = mailService;
        this.whatsappService = whatsappService;
        this.patientVaccinationRepository = patientVaccinationRepository;
        this.patientRepository = patientRepository;
        this.clinicProduct = clinicProduct;
        this.clinicVaccination = clinicVaccination;
        this.clinicMedication = clinicMedication;
        this.clinicConsumableEntity = clinicConsumableEntity;
        this.invoiceRepository = invoiceRepository;
        this.paymentDetailsRepository = paymentDetailsRepository;
        this.clinicRepository = clinicRepository;
        this.ownerBrandRepository = ownerBrandRepository;
        this.patientRemindersService = patientRemindersService;
        this.globalReminderService = globalReminderService;
        this.patientService = patientService;
        this.clinicLabReportService = clinicLabReportService;
        this.appointmentDetailsEntity = appointmentDetailsEntity;
        this.appointmentDoctorsEntity = appointmentDoctorsEntity;
    }
    async handle(message) {
        try {
            const body = JSON.parse(message.Body || '{}');
            const data = body.data;
            if (!data || !data.taskType) {
                this.logger.error('Invalid message format: missing taskType', body);
                return;
            }
            switch (data.taskType) {
                case 'processReminders':
                    await this.processReminders(data);
                    break;
                case 'generatePrescriptionPdfs':
                    await this.generatePrescriptionPdfs(data);
                    break;
                case 'updateInventory':
                    await this.updateInventory(data);
                    break;
                case 'updateInventoryForInvoiceUpdate':
                    await this.updateInventoryForInvoiceUpdate(data);
                    break;
                case 'generateVaccinationPdfs':
                    await this.generateVaccinationPdfs(data);
                    break;
                case 'generateVaccination':
                    await this.generateVaccination(data);
                    break;
                case 'processInvoiceDocument':
                    await this.processInvoiceDocument(data);
                    break;
                case 'processPaymentDocument':
                    await this.processPaymentDocument(data);
                    break;
                case 'updatePatientDetails':
                    await this.updatePatientDetails(data);
                    break;
                default:
                    this.logger.warn('Unknown taskType', {
                        taskType: data.taskType
                    });
            }
        }
        catch (error) {
            this.logger.error('Error processing task', error);
            throw error;
        }
    }
    // Download PDF from a URL
    async downloadPDF(url) {
        var _a, _b;
        try {
            if (!url) {
                this.logger.error('Cannot download PDF: URL is empty or undefined');
                throw new Error('Invalid URL: URL is empty or undefined');
            }
            this.logger.log('Downloading PDF from URL', {
                urlPresent: !!url,
                urlLength: (url === null || url === void 0 ? void 0 : url.length) || 0,
                urlStart: url ? url.substring(0, 50) + '...' : 'none'
            });
            const response = await axios_1.default.get(url, {
                responseType: 'arraybuffer',
                timeout: 15000, // 15 second timeout
                maxContentLength: 10 * 1024 * 1024 // 10MB max size
            });
            if (!response.data) {
                this.logger.error('PDF download failed: Empty response data', {
                    responseStatus: response.status,
                    contentType: response.headers['content-type']
                });
                throw new Error('PDF download failed: Empty response data');
            }
            const buffer = Buffer.from(response.data);
            if (buffer.length < 100) {
                // Sanity check - valid PDFs should be larger
                this.logger.warn('Downloaded PDF is suspiciously small', {
                    bufferSize: buffer.length,
                    responseStatus: response.status,
                    contentType: response.headers['content-type']
                });
            }
            this.logger.log('Successfully downloaded PDF', {
                responseStatus: response.status,
                bufferSize: buffer.length,
                contentType: response.headers['content-type']
            });
            return buffer;
        }
        catch (error) {
            const isAxiosError = axios_1.default.isAxiosError(error);
            this.logger.error('Error downloading PDF from URL', {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                urlPresent: !!url,
                urlFirstChars: url ? url.substring(0, 30) + '...' : 'none',
                isAxiosError,
                statusCode: isAxiosError ? (_a = error.response) === null || _a === void 0 ? void 0 : _a.status : undefined,
                responseData: isAxiosError
                    ? ((_b = error.response) === null || _b === void 0 ? void 0 : _b.data)
                        ? 'Present'
                        : 'Empty'
                    : undefined
            });
            throw new Error(`Failed to download PDF: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    // Process reminders
    async processReminders(data) {
        try {
            const { patientId, clinicId, brandId, invoiceDetails, invoiceId } = data;
            // Process reminders
            if (invoiceDetails && invoiceDetails.length > 0) {
                // Get all pending reminders
                const remindersResponse = await this.patientRemindersService.findAll(patientId, data.reminderStatus || 'PENDING');
                const reminderUpdates = [];
                // Process each invoice item
                for (const detail of invoiceDetails) {
                    // Find matching reminders
                    const matchingReminders = remindersResponse.reminders.filter(reminder => reminder.inventoryItemId ===
                        detail.inventoryId ||
                        reminder.inventoryType === detail.itemType);
                    // Process each matching reminder
                    for (const reminder of matchingReminders) {
                        const updatePromise = this.patientRemindersService
                            .completeReminder(reminder, detail.quantity || 1, data.appointmentId)
                            .catch(error => {
                            this.logger.error('Error completing reminder:', {
                                reminderId: reminder.id,
                                error
                            });
                            return null;
                        });
                        reminderUpdates.push(updatePromise);
                    }
                }
                // Wait for all updates to complete
                const results = await Promise.allSettled(reminderUpdates);
                // Add new global reminder processing
                if (clinicId) {
                    await this.globalReminderService.processInvoiceTriggers(patientId, clinicId, brandId, invoiceDetails, invoiceId);
                }
                // Log results
                const successful = results.filter(r => r.status === 'fulfilled').length;
                const failed = results.filter(r => r.status === 'rejected').length;
                this.logger.log('Reminder completion summary:', {
                    total: results.length,
                    successful,
                    failed
                });
            }
        }
        catch (error) {
            this.logger.error('Error processing reminders', error);
        }
    }
    // Generate required PDFs
    async generatePrescriptionPdfs(data) {
        var _a, _b, _c, _d, _e;
        try {
            // We now receive only essential identifiers
            const { patientId, invoiceId, alphaNumericReferenceId, alphaNumericPrescriptionReferenceId, prescriptionFileKey, doctorName, licenseNumber, doctorId, // We still receive doctorId but won't use User repository
            dischargeInstructions, appointmentId } = data;
            this.logger.log('Generating PDFs for invoice', {
                invoiceId,
                patientId,
                hasInvoiceRef: !!alphaNumericReferenceId,
                hasPrescriptionRef: !!alphaNumericPrescriptionReferenceId,
                hasDoctorId: !!doctorId
            });
            // Fetch all required data
            const invoice = await this.invoiceRepository.findOne({
                where: { id: invoiceId }
            });
            this.logger.log('Invoice', { invoice });
            if (!invoice) {
                this.logger.error('Invoice not found for PDF generation', {
                    invoiceId
                });
                return;
            }
            const patientDetail = await this.patientRepository.findOne({
                where: { id: patientId },
                relations: [
                    'clinic',
                    'clinic.brand',
                    'patientOwners',
                    'patientOwners.ownerBrand',
                    'patientOwners.ownerBrand.globalOwner'
                ]
            });
            if (!patientDetail) {
                this.logger.error('Patient not found for PDF generation', {
                    patientId
                });
                return;
            }
            // Instead of fetching the signature, just use what's in the invoice
            // or leave empty - the signature will be fetched from the appointment if needed
            let digitalSignature = '';
            // Use brandName in a basic log statement to prevent unused variable warning
            const brandName = ((_b = (_a = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _a === void 0 ? void 0 : _a.brand) === null || _b === void 0 ? void 0 : _b.name) || '';
            // Handle prescription generation if needed
            if (alphaNumericPrescriptionReferenceId && prescriptionFileKey) {
                // Get appointmentId from invoice
                const details = invoice.details || [];
                let extractedPrescriptionDetails = [];
                let prescriptionDischargeInstructions = dischargeInstructions || '';
                let formattedFollowUpDate = null;
                // Prioritize fetching from appointment details if we have an appointmentId
                if (appointmentId) {
                    try {
                        const appointmentDetailResponse = await this.appointmentDetailsEntity.findOne({
                            where: { appointment: { id: appointmentId } },
                            relations: ['appointment']
                        });
                        const appointmentDoctorResponse = await this.appointmentDoctorsEntity.findOne({
                            where: {
                                appointmentId: appointmentId,
                                primary: true
                            },
                            relations: [
                                'clinicUser',
                                'clinicUser.user',
                                'clinicUser.clinic.brand'
                            ]
                        });
                        digitalSignature =
                            ((_d = (_c = appointmentDoctorResponse === null || appointmentDoctorResponse === void 0 ? void 0 : appointmentDoctorResponse.clinicUser) === null || _c === void 0 ? void 0 : _c.user) === null || _d === void 0 ? void 0 : _d.digitalSignature) || '';
                        if (appointmentDetailResponse) {
                            const detailsObj = appointmentDetailResponse.details;
                            // Extract and format follow-up appointment date if present
                            const followUpRawData = (detailsObj === null || detailsObj === void 0 ? void 0 : detailsObj.followup) || null;
                            if (followUpRawData) {
                                formattedFollowUpDate = `Follow-up due: ${followUpRawData.label}.`;
                            }
                            // If appointment has prescription data, use it as preferred source
                            if (detailsObj === null || detailsObj === void 0 ? void 0 : detailsObj.prescription) {
                                const prescriptionList = detailsObj.prescription.list || [];
                                const dischargeInstruction = detailsObj.prescription.notes || '';
                                extractedPrescriptionDetails = prescriptionList
                                    .map((list) => ({
                                    name: list.name || '',
                                    comment: list.comment || '',
                                    dosage: list.dosage || '',
                                    brand: list.brand || '',
                                    form: list.form || '',
                                    unit: list.unit || ''
                                }))
                                    .filter((item) => item.name);
                                // Only override discharge instructions if available in appointment details
                                if (dischargeInstruction) {
                                    prescriptionDischargeInstructions =
                                        dischargeInstruction;
                                }
                                this.logger.log('Using prescription data from appointment details', {
                                    appointmentId,
                                    medicationCount: extractedPrescriptionDetails.length
                                });
                            }
                            else {
                                this.logger.log('No prescription data found in appointment details', {
                                    appointmentId
                                });
                            }
                        }
                        else {
                            this.logger.log('No appointment details found for appointment', {
                                appointmentId
                            });
                        }
                    }
                    catch (error) {
                        this.logger.error('Error fetching appointment details', {
                            appointmentId,
                            error: error instanceof Error
                                ? error.message
                                : String(error)
                        });
                    }
                }
                // Fall back to invoice details if no medications found in appointment
                if (extractedPrescriptionDetails.length === 0) {
                    extractedPrescriptionDetails = details
                        .filter((item) => item.addToPrescription === true &&
                        item.itemType === 'Medication' &&
                        item.name)
                        .map((item) => ({
                        name: item.name,
                        comment: item.comment || '',
                        dosage: item.dosage || '',
                        brand: item.brand || '',
                        form: item.form || '',
                        unit: item.unit || ''
                    }));
                    this.logger.log('Using fallback prescription data from invoice details', {
                        invoiceId: invoice.id,
                        medicationCount: extractedPrescriptionDetails.length
                    });
                }
                this.logger.log('Extracted prescription details', {
                    hasExtractedDetails: extractedPrescriptionDetails.length > 0,
                    extractedCount: extractedPrescriptionDetails.length,
                    firstItem: extractedPrescriptionDetails.length > 0
                        ? JSON.stringify(extractedPrescriptionDetails[0])
                        : 'none',
                    invoiceId: invoice.id
                });
                await this.generatePrescriptionPDF(patientDetail, {
                    prescriptionDetails: extractedPrescriptionDetails,
                    dischargeInstructions: prescriptionDischargeInstructions,
                    appointmentId,
                    followUpDate: formattedFollowUpDate
                }, prescriptionFileKey, doctorName || '', alphaNumericPrescriptionReferenceId, licenseNumber, digitalSignature // Pass empty string, the function will handle it
                );
            }
            // Add prescriptionFileKey to data object for distribution
            data.prescriptionFileKey = prescriptionFileKey;
            data.brandName = brandName;
            // Send emails and WhatsApp messages as needed
            if ((_e = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _e === void 0 ? void 0 : _e.length) {
                this.logger.log('Starting document distribution', {
                    hasOwners: true,
                    ownerCount: patientDetail.patientOwners.length
                });
                this.distributeDocuments(data, patientDetail);
            }
            else {
                this.logger.log('Skipping document distribution - no patient owners', {
                    patientId: invoice.patientId
                });
            }
        }
        catch (error) {
            this.logger.error('Error generating PDFs', error);
        }
    }
    // Update inventory based on items
    async updateInventory(data) {
        try {
            const { invoiceId } = data;
            this.logger.log('Updating inventory for invoice', { invoiceId });
            // Fetch the invoice with full details
            const invoice = await this.invoiceRepository.findOne({
                where: { id: invoiceId }
            });
            if (!invoice) {
                this.logger.error('Invoice not found for inventory update', {
                    invoiceId
                });
                return;
            }
            const details = invoice.details || [];
            if (!details || details.length === 0) {
                this.logger.warn('No details found for inventory update', {
                    invoiceId
                });
                return;
            }
            for (const item of details) {
                let repository;
                switch (item.itemType) {
                    case enum_plan_type_1.EnumPlanType.Consumable:
                        repository = this.clinicConsumableEntity;
                        break;
                    case enum_plan_type_1.EnumPlanType.Medication:
                        repository = this.clinicMedication;
                        break;
                    case enum_plan_type_1.EnumPlanType.Product:
                        repository = this.clinicProduct;
                        break;
                    case enum_plan_type_1.EnumPlanType.Vaccination:
                        repository = this.clinicVaccination;
                        break;
                    default:
                        continue;
                }
                const inventoryItem = await repository.findOne({
                    where: { id: item.inventoryId }
                });
                if (inventoryItem) {
                    if (inventoryItem.currentStock >= item.quantity) {
                        inventoryItem.currentStock -= item.quantity;
                    }
                    else {
                        inventoryItem.currentStock = 0;
                    }
                    await repository.save(inventoryItem);
                }
            }
        }
        catch (error) {
            this.logger.error('Error updating inventory', error);
        }
    }
    // Generate vaccination PDFs for existing vaccination records (NEW METHOD)
    async generateVaccinationPdfs(data) {
        var _a, _b, _c, _d;
        try {
            const { patientId, invoiceId, vaccinationRecordIds, doctorName, licenseNumber, doctorId } = data;
            this.logger.log('Generating vaccination PDFs for existing records', {
                invoiceId,
                patientId,
                vaccinationRecordIds,
                recordCount: (vaccinationRecordIds === null || vaccinationRecordIds === void 0 ? void 0 : vaccinationRecordIds.length) || 0
            });
            if (!vaccinationRecordIds || vaccinationRecordIds.length === 0) {
                this.logger.warn('No vaccination record IDs provided for PDF generation', {
                    invoiceId,
                    patientId
                });
                return;
            }
            // Fetch patient details
            const patientDetail = await this.patientRepository.findOne({
                where: { id: patientId },
                relations: [
                    'clinic',
                    'clinic.brand',
                    'patientOwners',
                    'patientOwners.ownerBrand',
                    'patientOwners.ownerBrand.globalOwner'
                ]
            });
            if (!patientDetail) {
                this.logger.error('Patient not found for vaccination PDF generation', {
                    patientId
                });
                return;
            }
            const brandName = ((_b = (_a = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _a === void 0 ? void 0 : _a.brand) === null || _b === void 0 ? void 0 : _b.name) || '';
            const digitalSignature = '';
            // Fetch vaccination records
            const vaccinationRecords = await this.patientVaccinationRepository.find({
                where: { id: (0, typeorm_2.In)(vaccinationRecordIds) }
            });
            if (vaccinationRecords.length === 0) {
                this.logger.error('No vaccination records found for PDF generation', {
                    vaccinationRecordIds
                });
                return;
            }
            // Fetch invoice to get vaccination details for PDF generation
            const invoice = await this.invoiceRepository.findOne({
                where: { id: invoiceId }
            });
            if (!invoice) {
                this.logger.error('Invoice not found for vaccination PDF generation', {
                    invoiceId
                });
                return;
            }
            const details = invoice.details || [];
            const vaccination = details.filter((list) => list.itemType === enum_plan_type_1.EnumPlanType.Vaccination);
            // Get cart-to-vaccination mapping from invoice metadata for robust matching
            const cartToVaccinationMapping = ((_c = invoice.metadata) === null || _c === void 0 ? void 0 : _c.cartToVaccinationMapping) || {};
            // Generate PDFs for each vaccination record
            for (const vaccinationRecord of vaccinationRecords) {
                try {
                    // Find the cart item ID that maps to this vaccination record ID
                    let cartItemId = null;
                    let vaccinationDetail = null;
                    let vaccinationIndex = -1;
                    // Search through the mapping to find the cart item ID for this vaccination record
                    for (const [cartId, vaccinationId] of Object.entries(cartToVaccinationMapping)) {
                        if (vaccinationId === vaccinationRecord.id) {
                            cartItemId = cartId;
                            break;
                        }
                    }
                    if (cartItemId) {
                        // Find the vaccination detail using the cart item ID
                        vaccinationDetail = vaccination.find((v) => v.id === cartItemId);
                        if (vaccinationDetail) {
                            vaccinationIndex =
                                vaccination.indexOf(vaccinationDetail);
                        }
                    }
                    if (!vaccinationDetail || vaccinationIndex === -1) {
                        this.logger.warn('Vaccination detail not found in invoice for record using cart mapping', {
                            vaccinationRecordId: vaccinationRecord.id,
                            cartItemId,
                            vaccinationName: vaccinationRecord.vaccineName,
                            hasMapping: !!cartItemId,
                            mappingKeys: Object.keys(cartToVaccinationMapping),
                            vaccinationCount: vaccination.length
                        });
                        continue;
                    }
                    // Generate PDF using existing method
                    const fileName = ((_d = vaccinationRecord.urlMeta) === null || _d === void 0 ? void 0 : _d.fileName) ||
                        `VAC_${vaccinationRecord.vaccineId}.pdf`;
                    const fileKey = await this.generateVaccinationPDF(patientDetail, invoice, doctorName, licenseNumber, vaccinationIndex, // Use the correct index from vaccination array
                    brandName, fileName, vaccinationRecord.vaccineId, digitalSignature);
                    if (fileKey) {
                        // Update vaccination record with PDF URL
                        await this.patientVaccinationRepository.update(vaccinationRecord.id, { reportUrl: fileKey });
                        this.logger.log('Successfully generated vaccination PDF and updated record', {
                            vaccinationRecordId: vaccinationRecord.id,
                            fileName,
                            fileKey
                        });
                        // Send email notification
                        this.sendVaccinationEmail(patientDetail, brandName, fileName, fileKey);
                        // Send WhatsApp notification
                        this.sendVaccinationWhatsApp(patientDetail, brandName, fileKey);
                    }
                }
                catch (recordError) {
                    this.logger.error('Error generating PDF for vaccination record', {
                        vaccinationRecordId: vaccinationRecord.id,
                        error: recordError
                    });
                }
            }
        }
        catch (error) {
            this.logger.error('Error generating vaccination PDFs', error);
        }
    }
    // Generate vaccination certificate and records (LEGACY METHOD - kept for backward compatibility)
    async generateVaccination(data) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        try {
            // We now receive only essential identifiers
            const { patientId, invoiceId, doctorName, licenseNumber, doctorId // We still receive doctorId but won't use User repository
             } = data;
            this.logger.log('Generating vaccination certificates', {
                invoiceId,
                patientId,
                hasDoctorId: !!doctorId
            });
            // Fetch required data
            const invoice = await this.invoiceRepository.findOne({
                where: { id: invoiceId }
            });
            if (!invoice) {
                this.logger.error('Invoice not found for vaccination generation', { invoiceId });
                return;
            }
            const patientDetail = await this.patientRepository.findOne({
                where: { id: patientId },
                relations: [
                    'clinic',
                    'clinic.brand',
                    'patientOwners',
                    'patientOwners.ownerBrand',
                    'patientOwners.ownerBrand.globalOwner'
                ]
            });
            if (!patientDetail) {
                this.logger.error('Patient not found for vaccination generation', { patientId });
                return;
            }
            // We won't fetch digitalSignature directly, leave empty
            const digitalSignature = '';
            const brandName = ((_b = (_a = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _a === void 0 ? void 0 : _a.brand) === null || _b === void 0 ? void 0 : _b.name) || '';
            const details = invoice.details || [];
            const vaccination = details.filter((list) => list.itemType === enum_plan_type_1.EnumPlanType.Vaccination);
            if (vaccination.length > 0) {
                // Get existing vaccination mappings to avoid duplicates
                const existingMappings = ((_c = invoice.metadata) === null || _c === void 0 ? void 0 : _c.cartToVaccinationMapping) || {};
                // Initialize vaccination mapping object for invoice metadata
                const cartToVaccinationMapping = {
                    ...existingMappings
                };
                for (let i = 0; i < vaccination.length; i++) {
                    const cartItemId = (_d = vaccination[i]) === null || _d === void 0 ? void 0 : _d.id;
                    // Check if this vaccination has already been generated
                    if (cartItemId && existingMappings[cartItemId]) {
                        this.logger.log('Skipping vaccination generation - already exists in metadata', {
                            invoiceId,
                            cartItemId,
                            vaccinationName: (_e = vaccination[i]) === null || _e === void 0 ? void 0 : _e.name,
                            existingPatientVaccinationId: existingMappings[cartItemId]
                        });
                        continue; // Skip this vaccination as it's already been generated
                    }
                    const fileName = `VAC_${await (0, generate_alpha_numeric_code_1.generateUniqueCode)('vaccineId', this.patientVaccinationRepository)}.pdf`;
                    const vaccineId = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('vaccineId', this.patientVaccinationRepository);
                    const fileKey = await this.generateVaccinationPDF(patientDetail, invoice, doctorName, licenseNumber, i, brandName, fileName, vaccineId, digitalSignature // Pass empty string, the function will handle it
                    );
                    if (fileKey) {
                        // Create vaccination record with appropriate typecasting
                        const vaccinationRecord = {
                            patientId: patientId,
                            systemGenerated: true,
                            vaccinationDate: new Date(),
                            vaccineName: ((_f = vaccination[i]) === null || _f === void 0 ? void 0 : _f.name) || 'Vaccination',
                            reportUrl: fileKey,
                            doctorName: doctorName,
                            urlMeta: {
                                fileType: 'PDF',
                                fileName: fileName
                            },
                            vaccineId: vaccineId,
                            appointmentId: invoice.appointmentId,
                            vaccinationId: (_g = vaccination[i]) === null || _g === void 0 ? void 0 : _g.inventoryId // Use inventoryId which corresponds to clinic_vaccinations.id
                        };
                        // Save vaccination record and get the saved record with ID
                        const savedVaccinationRecord = await this.patientVaccinationRepository.save(this.patientVaccinationRepository.create(vaccinationRecord));
                        // Map cart item ID to vaccination record ID
                        // Use the id field (which is the cart_item primary key) and patient vaccination ID
                        if (cartItemId && savedVaccinationRecord.id) {
                            cartToVaccinationMapping[cartItemId] =
                                savedVaccinationRecord.id;
                            this.logger.log('Successfully generated vaccination and updated mapping', {
                                invoiceId,
                                cartItemId,
                                patientVaccinationId: savedVaccinationRecord.id,
                                vaccinationName: (_h = vaccination[i]) === null || _h === void 0 ? void 0 : _h.name
                            });
                        }
                        // Send email notification
                        this.sendVaccinationEmail(patientDetail, brandName, fileName, fileKey);
                        // Send WhatsApp notification
                        this.sendVaccinationWhatsApp(patientDetail, brandName, fileKey);
                    }
                }
                try {
                    // Get current invoice metadata
                    const currentMetadata = invoice.metadata || {};
                    // Update metadata with vaccination mapping
                    const updatedMetadata = {
                        ...currentMetadata,
                        cartToVaccinationMapping
                    };
                    // Save updated metadata to invoice
                    invoice.metadata = updatedMetadata;
                    await this.invoiceRepository.save(invoice);
                    this.logger.log('Updated invoice metadata with new vaccination mappings', {
                        invoiceId,
                        totalMappings: Object.keys(cartToVaccinationMapping)
                            .length,
                        newMappings: Object.keys(cartToVaccinationMapping).length -
                            Object.keys(existingMappings).length
                    });
                }
                catch (metadataError) {
                    this.logger.error('Error updating invoice metadata with vaccination mappings', metadataError);
                }
            }
        }
        catch (error) {
            this.logger.error('Error generating vaccination certificates', error);
        }
    }
    // Process invoice document generation and sharing
    async processInvoiceDocument(data) {
        var _a;
        try {
            const { invoiceId, action, shareMethod, fileKey: providedFileKey, recipient, email: customEmail, phoneNumber: customPhoneNumber } = data;
            this.logger.log('Processing invoice document task', {
                invoiceId,
                action,
                providedFileKey,
                shareMethod,
                recipient,
                hasCustomEmail: !!customEmail,
                hasCustomPhone: !!customPhoneNumber
            });
            // Find the invoice by ID
            const invoice = await this.invoiceRepository.findOne({
                where: { id: invoiceId }
            });
            if (!invoice) {
                this.logger.error('Invoice not found', { invoiceId });
                return;
            }
            // Check if we've been provided a permanent file key to reuse
            // Permanent files start with 'invoice/' or 'creditNote/'
            const hasPermanentFileKey = providedFileKey &&
                (providedFileKey.startsWith('invoice/') ||
                    providedFileKey.startsWith('creditNote/'));
            // Check if the invoice already has a permanent file in its fileUrl
            const isInvoice = invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice;
            const fileUrl = invoice.fileUrl || {};
            const existingPermanentFileKey = isInvoice
                ? fileUrl.invoiceFileKey
                : fileUrl.creditNoteFileKey;
            // Validate if the existing file key is permanent (starts with 'invoice/' or 'creditNote/')
            const hasExistingPermanentFile = existingPermanentFileKey &&
                ((isInvoice &&
                    (existingPermanentFileKey.startsWith('invoice/') ||
                        existingPermanentFileKey.startsWith('invoice/'))) ||
                    (!isInvoice &&
                        existingPermanentFileKey.startsWith('creditNote/')));
            // Log important information about file keys
            this.logger.log('File key analysis', {
                invoiceId,
                action,
                hasPermanentFileKey,
                providedFileKey,
                existingPermanentFileKey,
                hasExistingPermanentFile,
                isInvoice,
                invoiceType: invoice.invoiceType
            });
            // Get patient details using the repository directly
            const patientDetails = await this.patientRepository.findOne({
                where: { id: invoice.patientId },
                relations: [
                    'clinic',
                    'clinic.brand',
                    'patientOwners',
                    'patientOwners.ownerBrand',
                    'patientOwners.ownerBrand.globalOwner'
                ]
            });
            if (!patientDetails) {
                this.logger.error('Patient details not found', {
                    patientId: invoice.patientId
                });
                return;
            }
            // Get clinic details
            const clinicDetails = patientDetails.clinic;
            if (!clinicDetails) {
                this.logger.error('Clinic details not found', {
                    patientId: invoice.patientId
                });
                return;
            }
            // Get owner details
            const patientOwner = (_a = patientDetails.patientOwners) === null || _a === void 0 ? void 0 : _a[0];
            if (!(patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand)) {
                this.logger.error('Owner details not found', {
                    patientId: invoice.patientId
                });
                return;
            }
            const ownerDetails = patientOwner.ownerBrand;
            // Extract pet name and owner last name for the filename
            const petNameForFile = patientDetails.patientName || 'pet';
            const ownerLastNameForFile = ownerDetails.lastName || 'owner';
            // Format pet name and owner last name for filename
            const formattedPetName = petNameForFile
                .replace(/[^a-zA-Z0-9]/g, '_')
                .toLowerCase();
            const formattedOwnerLastName = ownerLastNameForFile
                .replace(/[^a-zA-Z0-9]/g, '_')
                .toLowerCase();
            // For download filename format
            const fileNamePrefix = isInvoice
                ? `${formattedPetName}_${formattedOwnerLastName}_invoice`
                : `${formattedPetName}_${formattedOwnerLastName}_creditnote`;
            const fileName = `${fileNamePrefix}_${invoice.referenceAlphaId}.pdf`;
            // For email attachment format
            const emailAttachmentName = isInvoice
                ? `INVOICE_${invoice.referenceAlphaId}.pdf`
                : `CREDITNOTE_${invoice.referenceAlphaId}.pdf`;
            // If we have a permanent file key, skip PDF generation and use the existing file
            let pdfBuffer;
            let fileKey;
            this.logger.log('Process invoice document task params', {
                invoiceId: invoice.id,
                action,
                shareMethod,
                hasPermanentFileKey,
                providedFileKey,
                hasExistingPermanentFile,
                existingPermanentFileKey,
                invoiceType: invoice.invoiceType,
                isInvoice
            });
            // First priority: use the provided permanent file key if available
            // Second priority: use existing permanent file from the invoice record
            // Last resort: generate a new file
            if (hasPermanentFileKey) {
                // Use the provided permanent file key
                this.logger.log('Using provided permanent file for request', {
                    fileKey: providedFileKey,
                    action,
                    invoiceId: invoice.id,
                    isInvoice
                });
                fileKey = providedFileKey;
            }
            else if (hasExistingPermanentFile) {
                // Use the existing permanent file key from the invoice record
                this.logger.log('Using existing permanent file from invoice record', {
                    fileKey: existingPermanentFileKey,
                    action,
                    invoiceId: invoice.id,
                    isInvoice
                });
                fileKey = existingPermanentFileKey;
            }
            else {
                // No permanent file available, generate a new PDF
                this.logger.log('No permanent file available, generating new PDF', {
                    invoiceId: invoice.id,
                    action,
                    providedFileKey
                });
                // Generate a new PDF and upload it to S3
                pdfBuffer = await this.generateDocumentPdf(invoice, patientDetails, clinicDetails, ownerDetails);
                // Use provided temporary file key or generate a new one (no .pdf extension)
                fileKey =
                    providedFileKey ||
                        (isInvoice
                            ? `invoice_temp/${(0, uuidv7_1.uuidv4)()}`
                            : `creditNote_temp/${(0, uuidv7_1.uuidv4)()}`);
                // Upload PDF to S3
                await this.s3Service.uploadPdfToS3(pdfBuffer, fileKey);
                this.logger.log('PDF generated and uploaded to S3', {
                    invoiceId: invoice.id,
                    fileKey,
                    fileName,
                    isNewFile: true
                });
                // Update invoice with the file key
                if (!invoice.fileUrl) {
                    invoice.fileUrl = {};
                }
                // Reassign fileUrlData to use the newly initialized fileUrl object
                const fileUrlData = invoice.fileUrl;
                // Update the appropriate file key based on invoice type
                if (isInvoice) {
                    fileUrlData.invoiceFileKey = fileKey;
                    fileUrlData.isGenerating = 'false';
                }
                else {
                    fileUrlData.creditNoteFileKey = fileKey;
                    fileUrlData.isGenerating = 'false';
                }
                // Save the updated invoice
                await this.invoiceRepository.save(invoice);
            }
            // For share action, we need to download the PDF buffer if we don't already have it
            if (action === 'share' &&
                (hasPermanentFileKey || hasExistingPermanentFile) &&
                !pdfBuffer) {
                try {
                    this.logger.log('Attempting to get pre-signed URL for permanent file to share', {
                        fileKey,
                        invoiceId: invoice.id
                    });
                    const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
                    this.logger.log('Successfully got pre-signed URL', {
                        fileKey,
                        hasUrl: !!viewSignedUrl,
                        urlLength: (viewSignedUrl === null || viewSignedUrl === void 0 ? void 0 : viewSignedUrl.length) || 0
                    });
                    // Download the existing file
                    pdfBuffer = await this.downloadPDF(viewSignedUrl);
                    this.logger.log('Successfully downloaded permanent file for sharing', {
                        invoiceId: invoice.id,
                        fileKey,
                        bufferSize: (pdfBuffer === null || pdfBuffer === void 0 ? void 0 : pdfBuffer.length) || 0
                    });
                }
                catch (error) {
                    // Do not regenerate - log the error and let the process fail
                    this.logger.error('Error retrieving permanent file for sharing - NOT regenerating', {
                        error: error instanceof Error
                            ? error.message
                            : String(error),
                        stack: error instanceof Error
                            ? error.stack
                            : undefined,
                        fileKey,
                        invoiceId: invoice.id
                    });
                    // Exit without completing the task
                    return;
                }
            }
            // If this is a share action, handle sharing via email/WhatsApp
            if (action === 'share' && shareMethod) {
                // For share actions, we should have the PDF buffer by now
                if (!pdfBuffer) {
                    this.logger.error('Missing PDF buffer for sharing', {
                        invoiceId: invoice.id,
                        fileKey,
                        action,
                        shareMethod,
                        recipient
                    });
                    return;
                }
                // Now we should have a buffer one way or another
                this.logger.log('About to share invoice document', {
                    invoiceId: invoice.id,
                    fileKey,
                    shareMethod,
                    recipient,
                    hasCustomEmail: !!customEmail,
                    hasCustomPhone: !!customPhoneNumber,
                    hasPdfBuffer: !!pdfBuffer,
                    bufferSize: (pdfBuffer === null || pdfBuffer === void 0 ? void 0 : pdfBuffer.length) || 0
                });
                await this.shareInvoiceDocument(invoice, patientDetails, clinicDetails, ownerDetails, fileKey, emailAttachmentName, // Use the email-specific filename for attachment
                pdfBuffer, shareMethod, recipient, customEmail, customPhoneNumber);
                this.logger.log('Successfully shared invoice document', {
                    invoiceId: invoice.id,
                    fileKey,
                    shareMethod,
                    recipient
                });
            }
            this.logger.log('Invoice document task completed successfully', {
                invoiceId,
                action,
                fileKey,
                recipient
            });
        }
        catch (error) {
            // Enhanced error logging with detailed information
            this.logger.error('Error processing invoice document', {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                data: {
                    invoiceId: data === null || data === void 0 ? void 0 : data.invoiceId,
                    action: data === null || data === void 0 ? void 0 : data.action,
                    shareMethod: data === null || data === void 0 ? void 0 : data.shareMethod,
                    recipient: data === null || data === void 0 ? void 0 : data.recipient
                }
            });
        }
    }
    // Helper method to generate PDF document
    async generateDocumentPdf(invoice, patientDetails, clinicDetails, ownerDetails) {
        var _a, _b, _c;
        // Get payment details
        const paymentDetails = await this.paymentDetailsRepository.find({
            where: { invoiceId: invoice.id },
            order: { createdAt: 'ASC' }
        });
        // Get refund items if applicable
        let refundItems = [];
        if (invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Refund) {
            // This is already a refund invoice, no need to fetch additional items
        }
        else {
            // Check for any refund invoices linked to this invoice
            const refundInvoices = await this.invoiceRepository.find({
                where: {
                    cartId: invoice.cartId,
                    invoiceType: enum_invoice_types_1.EnumInvoiceType.Refund
                }
            });
            refundItems = refundInvoices.map(refundInvoice => ({
                creditNote: refundInvoice.referenceAlphaId || '',
                amount: Number(refundInvoice.invoiceAmount) || 0,
                date: moment(refundInvoice.createdAt).format('YYYY-MM-DD')
            }));
        }
        // Format payment items - separate cash/card payments from credit payments
        const normalPayments = paymentDetails.filter(payment => (payment.type === enum_credit_types_1.EnumAmountType.Invoice ||
            payment.type === enum_credit_types_1.EnumAmountType.ReconcileInvoice ||
            payment.type === enum_credit_types_1.EnumAmountType.BulkReconcileInvoice ||
            payment.type === enum_credit_types_1.EnumAmountType.Collect) &&
            Number(payment.amount) > 0 &&
            !payment.isCreditUsed);
        const creditPayments = paymentDetails.filter(payment => payment.isCreditUsed && Number(payment.creditAmountUsed) > 0);
        const paymentItems = [
            ...[...normalPayments, ...creditPayments]
                .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
                .map(payment => ({
                date: moment(payment.createdAt).format('YYYY-MM-DD HH:mm'),
                paymentMode: payment.isCreditUsed
                    ? 'Credits'
                    : payment.paymentType || '',
                receiptNumber: payment.referenceAlphaId || '',
                amount: payment.isCreditUsed
                    ? Number(payment.creditAmountUsed) || 0
                    : Number(payment.amount) || 0
            }))
        ];
        // Extract line items from invoice details where isAddedToCart is true
        const lineItems = (invoice.details || [])
            .filter((item) => item.isAddedToCart) // Filter to include only items where isAddedToCart is true
            .map((item) => ({
            description: item.name,
            quantity: item.quantity,
            price: Number(item.actualPrice)
        }));
        // Calculate pet details string
        const petDetails = patientDetails.species
            ? `${patientDetails.species || ''} - ${((_a = patientDetails.breed) === null || _a === void 0 ? void 0 : _a.split('_').join(' ')) || ''}`
                .toLowerCase()
                .replace(/\b\w/g, char => char.toUpperCase())
            : '';
        // Handle clinic logo URL
        let clinicLogoUrl = (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.logoUrl) || (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.clinicLogo) || '';
        // Get pre-signed URL for clinic logo if it exists and is an S3 path
        if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
            try {
                const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
                clinicLogoUrl = logoPreSignedUrl;
            }
            catch (error) {
                this.logger.error('Error generating pre-signed URL for clinic logo', error);
            }
        }
        // Generate the appropriate PDF HTML based on invoice type
        let pdfHtml = '';
        if (invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice) {
            // Prepare invoice data
            const invoiceData = {
                invoiceNumber: invoice.referenceAlphaId || '',
                invoiceDate: moment(invoice.createdAt).format('MMMM D, YYYY'),
                clinicName: clinicDetails.name || '',
                clinicAddress: this.getClinicAddress(patientDetails),
                clinicPhone: clinicDetails.phoneNumbers &&
                    clinicDetails.phoneNumbers.length > 0
                    ? clinicDetails.phoneNumbers[0].number || ''
                    : clinicDetails.mobile || '',
                clinicEmail: clinicDetails.email || '',
                clinicWebsite: clinicDetails.website || '',
                customerName: `${ownerDetails.firstName || ''} ${ownerDetails.lastName || ''}`,
                petName: patientDetails.patientName || '',
                petDetails,
                customerEmail: ownerDetails.email || '',
                customerPhone: ownerDetails.globalOwner
                    ? `${ownerDetails.globalOwner.countryCode}${ownerDetails.globalOwner.phoneNumber}`
                    : '',
                clinicLogoUrl: clinicLogoUrl,
                // Receipt details
                receiptDate: paymentDetails.length > 0
                    ? moment(paymentDetails[paymentDetails.length - 1]
                        .createdAt).format('Do MMM YYYY')
                    : '',
                paymentMode: invoice.paymentMode || '',
                receiptNumber: paymentDetails.length > 0
                    ? paymentDetails[paymentDetails.length - 1]
                        .referenceAlphaId || ''
                    : '',
                // Credits and refunds
                creditsUsed: paymentDetails.reduce((sum, payment) => sum + (Number(payment.creditAmountUsed) || 0), 0),
                refunds: refundItems.reduce((sum, refund) => sum + (refund.amount || 0), 0),
                refundCreditNote: refundItems.length > 0 ? refundItems[0].creditNote : '',
                refundAmount: refundItems.length > 0 ? refundItems[0].amount : 0,
                refundDate: refundItems.length > 0 ? refundItems[0].date : '',
                // Payment and refund items
                paymentItems,
                refundItems,
                // Invoice line items
                lineItems,
                // Invoice totals
                subtotal: Number(invoice.totalPrice) || 0,
                taxes: Number(invoice.totalTax) || 0,
                discount: Number(invoice.totalDiscount) || 0,
                previousBalance: Number(invoice.totalCredit) || 0,
                invoiceAmount: Number(invoice.invoiceAmount) || 0,
                totalDue: Number(invoice.invoiceAmount) || 0,
                amountPaid: Number(invoice.amountPaid) || 0,
                balanceDue: Number(invoice.balanceDue) || 0,
                // Writeoff and cancellation data from metadata
                writeoff: ((_b = invoice.metadata) === null || _b === void 0 ? void 0 : _b.writeoff)
                    ? {
                        amount: Number(invoice.metadata.writeoff.amount) || 0,
                        date: invoice.metadata.writeoff.date || '',
                        reason: invoice.metadata.writeoff.reason || '',
                        by: invoice.metadata.writeoff.by || ''
                    }
                    : undefined,
                cancellation: ((_c = invoice.metadata) === null || _c === void 0 ? void 0 : _c.cancellation)
                    ? {
                        amount: Number(invoice.metadata.cancellation.amount) ||
                            0,
                        date: invoice.metadata.cancellation.date || '',
                        reason: invoice.metadata.cancellation.reason || '',
                        by: invoice.metadata.cancellation.by || ''
                    }
                    : undefined
            };
            // Generate invoice HTML
            pdfHtml = (0, generateInvoice_1.generateInvoice)(invoiceData);
        }
        else if (invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Refund) {
            // Get original invoice
            const originalInvoice = await this.invoiceRepository.findOne({
                where: {
                    cartId: invoice.cartId,
                    invoiceType: enum_invoice_types_1.EnumInvoiceType.Invoice
                }
            });
            // Find specific payment details
            const creditNotePayment = paymentDetails.find(payment => payment.type === enum_credit_types_1.EnumAmountType.CreditNote);
            const collectPayment = paymentDetails.find(payment => payment.type === enum_credit_types_1.EnumAmountType.Collect &&
                payment.isCreditsAdded);
            // Prepare credit note data
            const creditNoteData = {
                creditNoteNumber: invoice.referenceAlphaId || '',
                creditNoteDate: moment(invoice.createdAt).format('MMMM D, YYYY'),
                clinicName: clinicDetails.name || '',
                clinicAddress: this.getClinicAddress(patientDetails),
                clinicPhone: clinicDetails.phoneNumbers &&
                    clinicDetails.phoneNumbers.length > 0
                    ? clinicDetails.phoneNumbers[0].number || ''
                    : clinicDetails.mobile || '',
                clinicEmail: clinicDetails.email || '',
                clinicWebsite: clinicDetails.website || '',
                customerName: `${ownerDetails.firstName || ''} ${ownerDetails.lastName || ''}`,
                petName: patientDetails.patientName || '',
                petDetails,
                lineItems,
                adjustments: Number(invoice.totalTax) + Number(invoice.totalDiscount) ||
                    0,
                totalDue: Number(invoice.amountPayable) || 0,
                amountPaid: creditNotePayment
                    ? Number(creditNotePayment.amount) || 0
                    : 0,
                balanceDue: Number(invoice.balanceDue) || 0,
                invoiceDate: originalInvoice
                    ? moment(originalInvoice.createdAt).format('MMMM D, YYYY')
                    : '',
                invoiceId: originalInvoice
                    ? originalInvoice.referenceAlphaId || ''
                    : '',
                clinicLogoUrl: clinicLogoUrl,
                refundAmount: Number(invoice.invoiceAmount) || 0,
                referenceInvoice: originalInvoice
                    ? originalInvoice.referenceAlphaId || ''
                    : '',
                // Credit Note payment receipt info
                receiptDate: creditNotePayment
                    ? moment(creditNotePayment.createdAt).format('Do MMM YYYY')
                    : moment(invoice.createdAt).format('Do MMM YYYY'),
                paymentMode: creditNotePayment
                    ? creditNotePayment.paymentType || ''
                    : invoice.paymentMode || '',
                receiptNumber: creditNotePayment
                    ? creditNotePayment.referenceAlphaId || ''
                    : '',
                // Collect payment receipt info (for credits)
                receiptNumberCredits: collectPayment
                    ? collectPayment.referenceAlphaId || ''
                    : '',
                creditsAdded: collectPayment
                    ? Number(collectPayment.creditAmountAdded) || 0
                    : 0
            };
            // Generate credit note HTML
            pdfHtml = (0, generateCreditNote_1.generateCreditNote)(creditNoteData);
        }
        // Generate PDF from HTML
        return await (0, generatePdf_1.generatePDF)(pdfHtml);
    }
    // Helper method to share invoice document via email/WhatsApp
    async shareInvoiceDocument(invoice, patientDetails, clinicDetails, ownerDetails, fileKey, emailAttachmentName, pdfBuffer, shareMethod, recipient = 'client', customEmail, customPhoneNumber) {
        var _a, _b, _c, _d;
        try {
            // Create view URL for WhatsApp sharing
            const viewUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
            // Handle email sharing
            if (shareMethod === 'email' || shareMethod === 'both') {
                // For custom email recipient
                if (recipient === 'other' && customEmail) {
                    const emailSubject = invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice
                        ? `Invoice ${invoice.referenceAlphaId} from ${clinicDetails.name}`
                        : `Credit Note ${invoice.referenceAlphaId} from ${clinicDetails.name}`;
                    const emailBody = `<p>Dear recipient,</p>
						<p>Please find attached the ${invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice ? 'invoice' : 'credit note'} from ${clinicDetails.name}.</p>
						<p>Thank you for choosing our services.</p>`;
                    await this.sendMail(emailBody, [pdfBuffer], [emailAttachmentName], customEmail, emailSubject);
                    this.logger.log(`${invoice.invoiceType} sent via email to custom recipient`, {
                        email: customEmail,
                        invoiceId: invoice.id
                    });
                }
                // For client recipient (default)
                else {
                    // Loop through all owners and send email to each one with an email address
                    for (const patientOwner of patientDetails.patientOwners ||
                        []) {
                        try {
                            const owner = patientOwner.ownerBrand;
                            if (!(owner === null || owner === void 0 ? void 0 : owner.email)) {
                                this.logger.warn('Owner has no email address, skipping email share', {
                                    ownerId: owner === null || owner === void 0 ? void 0 : owner.id,
                                    patientId: patientDetails.id
                                });
                                continue;
                            }
                            const emailSubject = invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice
                                ? `Invoice ${invoice.referenceAlphaId} from ${clinicDetails.name}`
                                : `Credit Note ${invoice.referenceAlphaId} from ${clinicDetails.name}`;
                            const emailBody = `<p>Dear ${owner.firstName},</p>
						<p>Please find attached the ${invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice ? 'invoice' : 'credit note'} from ${clinicDetails.name}.</p>
						<p>Thank you for choosing our services.</p>`;
                            await this.sendMail(emailBody, [pdfBuffer], [emailAttachmentName], owner.email, emailSubject);
                            this.logger.log(`${invoice.invoiceType} sent via email to ${owner.firstName} ${owner.lastName}`, {
                                email: owner.email,
                                invoiceId: invoice.id
                            });
                        }
                        catch (ownerError) {
                            this.logger.error('Error sending email to specific owner', {
                                error: ownerError instanceof Error
                                    ? ownerError.message
                                    : String(ownerError),
                                stack: ownerError instanceof Error
                                    ? ownerError.stack
                                    : undefined,
                                ownerId: (_a = patientOwner.ownerBrand) === null || _a === void 0 ? void 0 : _a.id,
                                invoiceId: invoice.id
                            });
                            // Continue with next owner even if one fails
                        }
                    }
                }
            }
            // Handle WhatsApp sharing
            if (shareMethod === 'whatsapp' || shareMethod === 'both') {
                // For custom phone number recipient
                if (recipient === 'other' && customPhoneNumber) {
                    try {
                        // Determine country code from phone number or default to +91
                        let countryCode = '+91';
                        let phoneNumber = customPhoneNumber;
                        // If the phone number already includes a country code (starts with +), extract it
                        if (customPhoneNumber.startsWith('+')) {
                            // Extract country code and number
                            const match = customPhoneNumber.match(/(\+\d+)(\d+)/);
                            if (match) {
                                countryCode = match[1];
                                phoneNumber = match[2];
                            }
                        }
                        const clientName = 'Recipient';
                        let templateData;
                        // Generate appropriate WhatsApp message based on invoice type
                        if (invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice) {
                            // Use invoice template
                            // Generate template data
                            const templateArgs = {
                                clientName,
                                petName: patientDetails.patientName || 'your pet',
                                brandName: clinicDetails.name,
                                doctorName: 'Your veterinarian',
                                invoiceFile: viewUrl,
                                mobileNumber: customPhoneNumber.startsWith('+')
                                    ? customPhoneNumber
                                    : `${countryCode}${phoneNumber}`
                            };
                            templateData = (0, template_helper_util_1.selectTemplate)(clinicDetails, templateArgs, whatsapp_template_generator_2.getAppointmentInvoiceTemplateData, whatsapp_template_generator_2.getAppointmentInvoiceClinicLinkTemplateData);
                        }
                        else {
                            // Use credit note template
                            const today = new Date();
                            const formattedDate = today.toLocaleDateString('en-US', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric'
                            });
                            // Generate template data
                            const templateArgs = {
                                clientName,
                                refundDate: formattedDate,
                                brandName: clinicDetails.name,
                                creditNoteFile: viewUrl,
                                mobileNumber: customPhoneNumber.startsWith('+')
                                    ? customPhoneNumber
                                    : `${countryCode}${phoneNumber}`
                            };
                            templateData = (0, template_helper_util_1.selectTemplate)(clinicDetails, templateArgs, whatsapp_template_generator_2.getCreditNoteGenerationTemplateData, whatsapp_template_generator_2.getCreditNoteGenerationClinicLinkTemplateData);
                        }
                        this.logger.log('Prepared WhatsApp template data for custom recipient', {
                            templateName: templateData.templateName,
                            mobileNumber: templateData.mobileNumber,
                            hasValues: !!templateData.valuesArray
                        });
                        // Send WhatsApp message if in production or UAT
                        if ((0, get_login_url_1.isProductionOrUat)()) {
                            await this.whatsappService.sendTemplateMessage({
                                templateName: templateData.templateName,
                                valuesArray: templateData.valuesArray,
                                mobileNumber: templateData.mobileNumber
                            });
                            this.logger.log(`${invoice.invoiceType} sent via WhatsApp successfully to custom recipient`, {
                                phoneNumber: customPhoneNumber,
                                invoiceId: invoice.id,
                                templateName: templateData.templateName
                            });
                        }
                        else {
                            this.logger.log('Skipping WhatsApp send (not in production/UAT)', {
                                templateName: templateData.templateName,
                                mobileNumber: templateData.mobileNumber
                            });
                        }
                    }
                    catch (whatsappError) {
                        this.logger.error('Error in WhatsApp sharing to custom recipient', {
                            error: whatsappError instanceof Error
                                ? whatsappError.message
                                : String(whatsappError),
                            stack: whatsappError instanceof Error
                                ? whatsappError.stack
                                : undefined,
                            invoiceId: invoice.id,
                            customPhoneNumber
                        });
                        // Continue execution to allow email to be sent even if WhatsApp fails
                    }
                }
                // For client recipient (default)
                else {
                    try {
                        // Get URL for viewing in WhatsApp
                        this.logger.log('Generated view URL for WhatsApp sharing', {
                            fileKey,
                            hasUrl: !!viewUrl
                        });
                        // Loop through all owners and send to each one with a phone number
                        for (const patientOwner of patientDetails.patientOwners ||
                            []) {
                            try {
                                const owner = patientOwner.ownerBrand;
                                if (!((_b = owner === null || owner === void 0 ? void 0 : owner.globalOwner) === null || _b === void 0 ? void 0 : _b.phoneNumber)) {
                                    this.logger.warn('Owner has no phone number, skipping WhatsApp share', {
                                        ownerId: owner === null || owner === void 0 ? void 0 : owner.id,
                                        patientId: patientDetails.id
                                    });
                                    continue;
                                }
                                const clientName = `${owner.firstName || ''} ${owner.lastName || ''}`;
                                let templateData;
                                // Generate appropriate WhatsApp message based on invoice type
                                if (invoice.invoiceType ===
                                    enum_invoice_types_1.EnumInvoiceType.Invoice) {
                                    // Generate template data
                                    const templateArgs = {
                                        clientName,
                                        petName: patientDetails.patientName ||
                                            'your pet',
                                        brandName: clinicDetails.name,
                                        doctorName: 'Your veterinarian',
                                        invoiceFile: viewUrl,
                                        mobileNumber: `${owner.globalOwner.countryCode}${owner.globalOwner.phoneNumber}`
                                    };
                                    templateData = (0, template_helper_util_1.selectTemplate)(clinicDetails, templateArgs, whatsapp_template_generator_2.getAppointmentInvoiceTemplateData, whatsapp_template_generator_2.getAppointmentInvoiceClinicLinkTemplateData);
                                }
                                else {
                                    // Use credit note template
                                    const today = new Date();
                                    const formattedDate = today.toLocaleDateString('en-US', {
                                        day: 'numeric',
                                        month: 'long',
                                        year: 'numeric'
                                    });
                                    // Generate template data
                                    const templateArgs = {
                                        clientName,
                                        refundDate: formattedDate,
                                        brandName: clinicDetails.name,
                                        creditNoteFile: viewUrl,
                                        mobileNumber: `${owner.globalOwner.countryCode}${owner.globalOwner.phoneNumber}`
                                    };
                                    templateData = (0, template_helper_util_1.selectTemplate)(clinicDetails, templateArgs, whatsapp_template_generator_2.getCreditNoteGenerationTemplateData, whatsapp_template_generator_2.getCreditNoteGenerationClinicLinkTemplateData);
                                }
                                this.logger.log('Prepared WhatsApp template data', {
                                    templateName: templateData.templateName,
                                    mobileNumber: templateData.mobileNumber,
                                    hasValues: !!templateData.valuesArray
                                });
                                // Send WhatsApp message if in production or UAT
                                if ((0, get_login_url_1.isProductionOrUat)()) {
                                    await this.whatsappService.sendTemplateMessage({
                                        templateName: templateData.templateName,
                                        valuesArray: templateData.valuesArray,
                                        mobileNumber: templateData.mobileNumber
                                    });
                                    this.logger.log(`${invoice.invoiceType} sent via WhatsApp successfully to ${clientName}`, {
                                        phoneNumber: (_c = owner.globalOwner) === null || _c === void 0 ? void 0 : _c.phoneNumber,
                                        invoiceId: invoice.id,
                                        templateName: templateData.templateName
                                    });
                                }
                                else {
                                    this.logger.log('Skipping WhatsApp send (not in production/UAT)', {
                                        templateName: templateData.templateName,
                                        mobileNumber: templateData.mobileNumber
                                    });
                                }
                            }
                            catch (ownerError) {
                                this.logger.error('Error processing WhatsApp for specific owner', {
                                    error: ownerError instanceof Error
                                        ? ownerError.message
                                        : String(ownerError),
                                    stack: ownerError instanceof Error
                                        ? ownerError.stack
                                        : undefined,
                                    ownerId: (_d = patientOwner.ownerBrand) === null || _d === void 0 ? void 0 : _d.id,
                                    invoiceId: invoice.id
                                });
                                // Continue with next owner even if one fails
                            }
                        }
                    }
                    catch (whatsappError) {
                        this.logger.error('Error in WhatsApp sharing logic', {
                            error: whatsappError instanceof Error
                                ? whatsappError.message
                                : String(whatsappError),
                            stack: whatsappError instanceof Error
                                ? whatsappError.stack
                                : undefined,
                            invoiceId: invoice.id
                        });
                        // Still throw to be caught by outer catch
                        throw whatsappError;
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('Error sharing invoice document', {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                invoiceId: invoice.id,
                shareMethod
            });
            throw error; // Re-throw for the calling method to handle
        }
    }
    // Helper method to format clinic address
    getClinicAddress(entityWithAddress) {
        const addressParts = [];
        // Check if we're dealing with a patient object or direct clinic object
        const clinic = entityWithAddress.clinic || entityWithAddress;
        if (clinic === null || clinic === void 0 ? void 0 : clinic.addressLine1) {
            addressParts.push(clinic.addressLine1);
        }
        if (clinic === null || clinic === void 0 ? void 0 : clinic.city) {
            addressParts.push(clinic.city);
        }
        if (clinic === null || clinic === void 0 ? void 0 : clinic.addressPincode) {
            addressParts.push(`- ${clinic.addressPincode}`);
        }
        if (clinic === null || clinic === void 0 ? void 0 : clinic.state) {
            addressParts.push(clinic.state);
        }
        return addressParts.join(', ').trim();
    }
    // Helper method to calculate age in years and months
    calculateAge(dob) {
        const birthDate = moment(dob, 'DD MMM YYYY');
        const today = moment();
        const years = today.diff(birthDate, 'years');
        const months = today.diff(birthDate.add(years, 'years'), 'months');
        // Return formatted age, omitting years if they are 0
        return years > 0 ? `${years}y & ${months}m` : `${months}m`;
    }
    // Helper method to send mail
    async sendMail(body, buffers, fileName, email, subject) {
        try {
            if ((0, get_login_url_1.isProduction)() && email) {
                this.mailService.sendMail({
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'Attachments',
                    pdfBuffers: buffers,
                    pdfFileNames: fileName,
                    toMailAddress: email
                });
            }
            else if (!(0, get_login_url_1.isProduction)()) {
                this.mailService.sendMail({
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'Attachments',
                    pdfBuffers: buffers,
                    pdfFileNames: fileName,
                    toMailAddress: constants_1.DEV_SES_EMAIL
                });
            }
        }
        catch (error) {
            this.logger.error('Send Mail Error', error);
        }
    }
    // Helper method to generate prescription PDF
    async generatePrescriptionPDF(patientDetail, prescriptionDetailObject, fileKey, doctorName, prescriptionReferenceId, licenseNumber, digitalSignature) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5;
        try {
            // Log the appointment ID when available
            const appointmentId = prescriptionDetailObject === null || prescriptionDetailObject === void 0 ? void 0 : prescriptionDetailObject.appointmentId;
            if (appointmentId) {
                this.logger.log('Generating prescription PDF for appointment', {
                    appointmentId,
                    prescriptionReferenceId
                });
            }
            // Use the already formatted prescription details directly
            const medicationList = ((_a = prescriptionDetailObject === null || prescriptionDetailObject === void 0 ? void 0 : prescriptionDetailObject.prescriptionDetails) === null || _a === void 0 ? void 0 : _a.map((item) => ({
                medication: item.name || '',
                comments: item.comment || '',
                dosage: item.dosage || '',
                brand: item.brand || '',
                form: item.form || '',
                unit: item.unit || ''
            }))) || [];
            const hasMedicationItems = medicationList.length > 0;
            const hasDischargeInstructions = !!(prescriptionDetailObject === null || prescriptionDetailObject === void 0 ? void 0 : prescriptionDetailObject.dischargeInstructions) &&
                prescriptionDetailObject.dischargeInstructions.trim().length >
                    0;
            if (hasMedicationItems || hasDischargeInstructions) {
                const petDetails = patientDetail.species
                    ? `${patientDetail.species || ''} - ${((_b = patientDetail.breed) === null || _b === void 0 ? void 0 : _b.split('_').join(' ')) || ''}`
                        .toLowerCase()
                        .replace(/\b\w/g, char => char.toUpperCase())
                    : '';
                let clinicLogoUrl = ((_c = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _c === void 0 ? void 0 : _c.logoUrl) ||
                    ((_d = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _d === void 0 ? void 0 : _d.clinicLogo) ||
                    '';
                // Get pre-signed URL for clinic logo if it exists and is an S3 path
                if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
                    try {
                        const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
                        clinicLogoUrl = logoPreSignedUrl;
                    }
                    catch (error) {
                        this.logger.error('Error generating pre-signed URL for clinic logo', error);
                    }
                }
                const prescriptionData = {
                    clinicLogoUrl: clinicLogoUrl,
                    prescriptionNumber: prescriptionReferenceId,
                    prescriptionId: prescriptionReferenceId,
                    prescriptionDate: ((_e = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.appointment) === null || _e === void 0 ? void 0 : _e.date)
                        ? moment(patientDetail.appointment.date).format('DD MMM YYYY')
                        : moment().format('DD MMM YYYY'),
                    clinicName: ((_f = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _f === void 0 ? void 0 : _f.name) || '',
                    clinicAddress: this.getClinicAddress(patientDetail),
                    clinicPhone: ((_g = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _g === void 0 ? void 0 : _g.phoneNumbers) &&
                        ((_h = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _h === void 0 ? void 0 : _h.phoneNumbers.length) > 0
                        ? ((_j = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _j === void 0 ? void 0 : _j.phoneNumbers[0].number) ||
                            ''
                        : ((_k = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _k === void 0 ? void 0 : _k.mobile) || '',
                    clinicEmail: ((_l = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _l === void 0 ? void 0 : _l.email) || '',
                    clinicWebsite: ((_m = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _m === void 0 ? void 0 : _m.website) || '',
                    customerName: `${((_q = (_p = (_o = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _o === void 0 ? void 0 : _o[0]) === null || _p === void 0 ? void 0 : _p.ownerBrand) === null || _q === void 0 ? void 0 : _q.firstName) || ''} ${((_t = (_s = (_r = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _r === void 0 ? void 0 : _r[0]) === null || _s === void 0 ? void 0 : _s.ownerBrand) === null || _t === void 0 ? void 0 : _t.lastName) || ''}`,
                    petName: (patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientName) || '',
                    petDetails,
                    lineItems: medicationList || [],
                    dischargeInstructions: (_u = prescriptionDetailObject === null || prescriptionDetailObject === void 0 ? void 0 : prescriptionDetailObject.dischargeInstructions) !== null && _u !== void 0 ? _u : '',
                    vetName: doctorName,
                    vetLicenseNo: licenseNumber !== null && licenseNumber !== void 0 ? licenseNumber : '',
                    digitalSignature: digitalSignature !== null && digitalSignature !== void 0 ? digitalSignature : '',
                    customerEmail: ((_x = (_w = (_v = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _v === void 0 ? void 0 : _v[0]) === null || _w === void 0 ? void 0 : _w.ownerBrand) === null || _x === void 0 ? void 0 : _x.email) ||
                        '',
                    customerPhone: `${(_1 = (_0 = (_z = (_y = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _y === void 0 ? void 0 : _y[0]) === null || _z === void 0 ? void 0 : _z.ownerBrand) === null || _0 === void 0 ? void 0 : _0.globalOwner) === null || _1 === void 0 ? void 0 : _1.countryCode}${(_5 = (_4 = (_3 = (_2 = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _2 === void 0 ? void 0 : _2[0]) === null || _3 === void 0 ? void 0 : _3.ownerBrand) === null || _4 === void 0 ? void 0 : _4.globalOwner) === null || _5 === void 0 ? void 0 : _5.phoneNumber}`,
                    // Add follow-up date if present
                    followUpDate: (prescriptionDetailObject === null || prescriptionDetailObject === void 0 ? void 0 : prescriptionDetailObject.followUpDate) || null
                };
                try {
                    const prescriptionHtml = (0, generatePrescription_1.generatePrescription)(prescriptionData);
                    try {
                        const pdfBuffer = await (0, generatePdf_1.generatePDF)(prescriptionHtml);
                        try {
                            await this.s3Service.uploadPdfToS3(pdfBuffer, fileKey);
                            this.logger.log('Prescription PDF uploaded to S3 successfully', {
                                fileKey
                            });
                        }
                        catch (uploadError) {
                            this.logger.error('Error uploading prescription PDF to S3', {
                                error: uploadError
                            });
                            throw uploadError;
                        }
                    }
                    catch (pdfError) {
                        this.logger.error('Error generating prescription PDF buffer', {
                            error: pdfError
                        });
                        throw pdfError;
                    }
                }
                catch (htmlError) {
                    this.logger.error('Error generating prescription HTML', {
                        error: htmlError
                    });
                    throw htmlError;
                }
            }
            else {
                this.logger.log('No prescription content to generate PDF');
            }
        }
        catch (error) {
            this.logger.error('Error generating prescription PDF', {
                error,
                patientId: patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.id
            });
        }
    }
    // Helper method to generate vaccination certificate PDF
    async generateVaccinationPDF(patientDetail, createInvoiceDto, doctorName, licenseNumber, index, brandName, fileName, vaccineId, digitalSignature) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9;
        try {
            const vaccinations = (_a = createInvoiceDto === null || createInvoiceDto === void 0 ? void 0 : createInvoiceDto.details) === null || _a === void 0 ? void 0 : _a.filter((list) => list.itemType === enum_plan_type_1.EnumPlanType.Vaccination);
            let clinicLogoUrl = ((_b = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _b === void 0 ? void 0 : _b.logoUrl) ||
                ((_c = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _c === void 0 ? void 0 : _c.clinicLogo) ||
                '';
            // Get pre-signed URL for clinic logo if it exists and is an S3 path
            if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
                try {
                    const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
                    clinicLogoUrl = logoPreSignedUrl;
                }
                catch (error) {
                    this.logger.error('Error generating pre-signed URL for clinic logo', error);
                }
            }
            console.log(clinicLogoUrl, 'clinicLogoUrl');
            const vaccinationData = {
                clinicLogoUrl: clinicLogoUrl,
                vaccineName: (_d = vaccinations[index]) === null || _d === void 0 ? void 0 : _d.name,
                vaccinationDate: moment().format('DD MMM YYYY'),
                species: (patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.species) || '',
                breed: ((_e = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.breed) === null || _e === void 0 ? void 0 : _e.split('_').join(' ').toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase())) || '',
                color: patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.identification,
                weight: (patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.weight) + ' kg' || '20 kg',
                reproductiveStatus: (patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.reproductiveStatus) || '',
                dob: (patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.age) || '',
                age: String(this.calculateAge(patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.age)),
                petName: (patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientName) || '',
                ownerName: `${((_h = (_g = (_f = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _f === void 0 ? void 0 : _f[0]) === null || _g === void 0 ? void 0 : _g.ownerBrand) === null || _h === void 0 ? void 0 : _h.firstName) || ''} ${((_l = (_k = (_j = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _j === void 0 ? void 0 : _j[0]) === null || _k === void 0 ? void 0 : _k.ownerBrand) === null || _l === void 0 ? void 0 : _l.lastName) || ''}`,
                clinicName: ((_m = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _m === void 0 ? void 0 : _m.name) || '',
                clinicAddress: this.getClinicAddress(patientDetail),
                clinicPhone: ((_o = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _o === void 0 ? void 0 : _o.phoneNumbers) &&
                    ((_p = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _p === void 0 ? void 0 : _p.phoneNumbers.length) > 0
                    ? ((_q = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _q === void 0 ? void 0 : _q.phoneNumbers[0].number) || ''
                    : ((_r = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _r === void 0 ? void 0 : _r.mobile) || '',
                clinicEmail: ((_s = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _s === void 0 ? void 0 : _s.email) || '',
                clinicWebsite: ((_t = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _t === void 0 ? void 0 : _t.website) || '',
                vetName: doctorName,
                vetLicense: licenseNumber,
                digitalSignature: digitalSignature !== null && digitalSignature !== void 0 ? digitalSignature : '',
                gender: (patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.gender) || '',
                vaccineId,
                ownerEmail: ((_w = (_v = (_u = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _u === void 0 ? void 0 : _u[0]) === null || _v === void 0 ? void 0 : _v.ownerBrand) === null || _w === void 0 ? void 0 : _w.email) || '',
                ownerPhone: `${(_0 = (_z = (_y = (_x = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _x === void 0 ? void 0 : _x[0]) === null || _y === void 0 ? void 0 : _y.ownerBrand) === null || _z === void 0 ? void 0 : _z.globalOwner) === null || _0 === void 0 ? void 0 : _0.countryCode}${(_4 = (_3 = (_2 = (_1 = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _1 === void 0 ? void 0 : _1[0]) === null || _2 === void 0 ? void 0 : _2.ownerBrand) === null || _3 === void 0 ? void 0 : _3.globalOwner) === null || _4 === void 0 ? void 0 : _4.phoneNumber}`,
                // Add missing required properties
                ownerAddress: ((_7 = (_6 = (_5 = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _5 === void 0 ? void 0 : _5[0]) === null || _6 === void 0 ? void 0 : _6.ownerBrand) === null || _7 === void 0 ? void 0 : _7.address) ||
                    '',
                manufacturer: ((_8 = vaccinations[index]) === null || _8 === void 0 ? void 0 : _8.manufacturer) || '',
                batchNo: ((_9 = vaccinations[index]) === null || _9 === void 0 ? void 0 : _9.batchNo) || ''
                // duration: vaccinations[index]?.duration || '1 year',
                // lotExpiration:
                // 	vaccinations[index]?.lotExpiration ||
                // 	moment().add(1, 'year').format('DD MMM YYYY')
            };
            const VaccinationHTML = (0, generateVaccinationCertificate_1.generateVaccinationCertificate)(vaccinationData);
            const pdfBuffer = await (0, generatePdf_1.generatePDF)(VaccinationHTML);
            const fileKey = `vaccinations/${(0, uuidv7_1.uuidv4)()}`;
            await this.s3Service.uploadPdfToS3(pdfBuffer, fileKey);
            return fileKey;
        }
        catch (error) {
            this.logger.error('Error generating vaccination PDF', error);
            return null;
        }
    }
    // Send vaccination email
    async sendVaccinationEmail(patientDetail, brandName, fileName, fileKey) {
        try {
            patientDetail.patientOwners.forEach(async (patientOwner) => {
                var _a, _b, _c, _d;
                if ((_a = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _a === void 0 ? void 0 : _a.email) {
                    const { body, subject, toMailAddress } = (0, mail_template_generator_1.appointmentCompletionWithVaccinationMailGenerator)({
                        brandName,
                        email: (_b = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _b === void 0 ? void 0 : _b.email,
                        firstname: (_c = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _c === void 0 ? void 0 : _c.firstName,
                        lastName: (_d = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _d === void 0 ? void 0 : _d.lastName,
                        petName: patientDetail.patientName
                    });
                    const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
                    const pdfBuffer = await this.downloadPDF(viewSignedUrl);
                    this.sendMail(body, [pdfBuffer], [fileName], toMailAddress, subject);
                }
            });
        }
        catch (error) {
            this.logger.error('Error sending vaccination email', error);
        }
    }
    // Send vaccination WhatsApp
    async sendVaccinationWhatsApp(patientDetail, brandName, fileKey) {
        try {
            await this.sendAppointmentVaccinationWhatsappMessage({
                brandName,
                patientDetails: patientDetail,
                vaccinationFileKey: fileKey
            });
        }
        catch (error) {
            this.logger.error('Error sending vaccination WhatsApp', error);
        }
    }
    // Distribute documents via email and WhatsApp
    async distributeDocuments(data, patientDetail) {
        var _a, _b, _c;
        try {
            const { brandName, doctorName, prescriptionFileKey } = data;
            this.logger.log('Starting document distribution', {
                hasPatientOwners: !!(patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners),
                ownerCount: ((_a = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners) === null || _a === void 0 ? void 0 : _a.length) || 0
            });
            // Send prescriptions (via email and WhatsApp)
            if (prescriptionFileKey) {
                this.logger.log('Processing prescription distribution', {
                    prescriptionFileKey
                });
                let emailSent = false;
                let whatsappSent = false;
                patientDetail.patientOwners.forEach(async (patientOwner) => {
                    var _a, _b, _c, _d;
                    if ((_a = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _a === void 0 ? void 0 : _a.email) {
                        try {
                            const { body, subject, toMailAddress } = (0, mail_template_generator_1.appointmentCompletionMailGenerator)({
                                brandName,
                                doctorName,
                                email: (_b = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _b === void 0 ? void 0 : _b.email,
                                firstname: (_c = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _c === void 0 ? void 0 : _c.firstName,
                                lastName: (_d = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _d === void 0 ? void 0 : _d.lastName,
                                petName: patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientName
                            });
                            const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(prescriptionFileKey);
                            const pdfBuffer = await this.downloadPDF(viewSignedUrl);
                            this.sendMail(body, [pdfBuffer], [
                                `PRES_${data.alphaNumericPrescriptionReferenceId}.pdf`
                            ], toMailAddress, subject);
                            emailSent = true;
                            this.logger.log('Prescription email sent successfully', {
                                prescriptionId: data.alphaNumericPrescriptionReferenceId ||
                                    'unknown'
                            });
                        }
                        catch (emailError) {
                            this.logger.error('Error sending prescription email', {
                                error: emailError.message
                            });
                        }
                    }
                });
                // Send WhatsApp messages to all owners
                for (const patientOwner of patientDetail.patientOwners) {
                    if (patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) {
                        try {
                            await this.sendAppointmentConfirmationWhatsappMessage({
                                brandName,
                                doctorName,
                                patientDetails: {
                                    ...patientDetail,
                                    patientOwners: [patientOwner] // Send to individual owner
                                },
                                prescriptionFileKey
                            });
                            whatsappSent = true;
                            this.logger.log('Prescription WhatsApp message sent successfully', {
                                ownerId: ((_b = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _b === void 0 ? void 0 : _b.id) ||
                                    'unknown'
                            });
                        }
                        catch (whatsappError) {
                            this.logger.error('Error sending prescription WhatsApp', {
                                error: whatsappError.message,
                                ownerId: ((_c = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _c === void 0 ? void 0 : _c.id) ||
                                    'unknown'
                            });
                        }
                    }
                }
                this.logger.log('Prescription distribution complete', {
                    emailSent,
                    whatsappSent
                });
            }
            else {
                this.logger.log('Skipping prescription distribution - no prescription file');
            }
        }
        catch (error) {
            this.logger.error('Error distributing documents', {
                error: error.message
            });
        }
    }
    // Send appointment confirmation WhatsApp message
    async sendAppointmentConfirmationWhatsappMessage({ patientDetails, brandName, doctorName, prescriptionFileKey }) {
        var _a, _b;
        try {
            if (!((_b = (_a = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.ownerBrand)) {
                throw new Error('Missing patient owner details');
            }
            patientDetails.patientOwners.forEach(async (patientOwner) => {
                var _a;
                const owner = patientOwner.ownerBrand;
                if ((_a = owner === null || owner === void 0 ? void 0 : owner.globalOwner) === null || _a === void 0 ? void 0 : _a.phoneNumber) {
                    const firstName = owner === null || owner === void 0 ? void 0 : owner.firstName;
                    const lastName = owner === null || owner === void 0 ? void 0 : owner.lastName;
                    const ownerMobileNumber = `${owner.globalOwner.countryCode}${owner.globalOwner.phoneNumber}`;
                    if (prescriptionFileKey) {
                        await this.sendPrescriptionMessage({
                            patientDetails,
                            brandName,
                            doctorName,
                            prescriptionFileKey,
                            ownerMobileNumber,
                            firstName,
                            lastName
                        });
                    }
                }
            });
        }
        catch (error) {
            this.logger.error('Error in appointment confirmation message process', error);
        }
    }
    // Send prescription message via WhatsApp
    async sendPrescriptionMessage({ patientDetails, brandName, doctorName, prescriptionFileKey, ownerMobileNumber, firstName, lastName }) {
        try {
            const prescriptionCloudFrontUrl = await this.s3Service.getViewPreSignedUrl(prescriptionFileKey);
            if (!prescriptionCloudFrontUrl) {
                throw new Error('Failed to generate prescription Cloudfront URL');
            }
            // Generate template data
            const templateArgs = {
                doctorName,
                brandName,
                petName: patientDetails.patientName,
                clientName: `${firstName} ${lastName}`,
                mobileNumber: ownerMobileNumber,
                prescriptionFile: prescriptionCloudFrontUrl
            };
            const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)(patientDetails.clinic, templateArgs, whatsapp_template_generator_1.getAppointmentPrescriptionTemplateData, whatsapp_template_generator_1.getAppointmentPrescriptionClinicLinkTemplateData);
            if ((0, get_login_url_1.isProductionOrUat)()) {
                await this.whatsappService.sendTemplateMessage({
                    templateName,
                    valuesArray,
                    mobileNumber
                });
                this.logger.log('Prescription sent via WhatsApp successfully', {
                    recipientName: `${firstName} ${lastName}`,
                    phoneNumber: ownerMobileNumber,
                    templateName
                });
            }
        }
        catch (error) {
            this.logger.error('Failed to send prescription WhatsApp message', error instanceof Error
                ? { message: error.message, stack: error.stack }
                : error);
        }
    }
    // Send vaccination certificate WhatsApp message
    async sendAppointmentVaccinationWhatsappMessage({ patientDetails, brandName, vaccinationFileKey }) {
        var _a, _b;
        try {
            if (!((_b = (_a = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.ownerBrand)) {
                throw new Error('Missing patient owner details');
            }
            patientDetails.patientOwners.forEach(async (patientOwner) => {
                var _a;
                const owner = patientOwner.ownerBrand;
                if ((_a = owner === null || owner === void 0 ? void 0 : owner.globalOwner) === null || _a === void 0 ? void 0 : _a.phoneNumber) {
                    const firstName = owner === null || owner === void 0 ? void 0 : owner.firstName;
                    const lastName = owner === null || owner === void 0 ? void 0 : owner.lastName;
                    const ownerMobileNumber = `${owner.globalOwner.countryCode}${owner.globalOwner.phoneNumber}`;
                    const vaccinationCloundFrontUrl = await this.s3Service.getViewPreSignedUrl(vaccinationFileKey);
                    if (!vaccinationCloundFrontUrl) {
                        throw new Error('Failed to generate vaccination Cloudfront URL');
                    }
                    // Generate template data
                    const templateArgs = {
                        brandName,
                        petName: patientDetails.patientName,
                        clientName: `${firstName} ${lastName}`,
                        mobileNumber: ownerMobileNumber,
                        vaccinationFile: vaccinationCloundFrontUrl
                    };
                    const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)(patientDetails.clinic, templateArgs, whatsapp_template_generator_1.getAppointmentVaccinationCertificateTemplateData, whatsapp_template_generator_1.getAppointmentVaccinationCertificateClinicLinkTemplateData);
                    if ((0, get_login_url_1.isProductionOrUat)()) {
                        await this.whatsappService.sendTemplateMessage({
                            templateName,
                            valuesArray,
                            mobileNumber
                        });
                    }
                }
            });
        }
        catch (error) {
            this.logger.error('Error in vaccination certificate WhatsApp message process', error);
        }
    }
    // Process payment document generation and sharing
    async processPaymentDocument(data) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        try {
            const { referenceAlphaId, action, shareMethod, brandId, userId, fileKey: providedFileKey, recipient, email: customEmail, phoneNumber: customPhoneNumber } = data;
            this.logger.log('Processing payment document task', {
                referenceAlphaId,
                action,
                providedFileKey,
                shareMethod,
                brandId,
                recipient,
                hasCustomEmail: !!customEmail,
                hasCustomPhone: !!customPhoneNumber
            });
            // Find all payment details with this reference ID
            const paymentDetails = await this.paymentDetailsRepository.find({
                where: {
                    referenceAlphaId,
                    brandId
                },
                relations: ['invoice', 'ownerBrand']
            });
            if (!paymentDetails || paymentDetails.length === 0) {
                this.logger.error('Payment details not found', {
                    referenceAlphaId,
                    brandId
                });
                return;
            }
            // Get the first payment detail to extract common information
            const firstPayment = paymentDetails[0];
            // Check if we've been provided a permanent file key to reuse
            // Permanent files start with 'receipt/'
            const hasPermanentFileKey = providedFileKey && providedFileKey.startsWith('receipt/');
            // Check if any of the payment details already has a permanent file in its receiptDetail
            let hasExistingPermanentFile = false;
            let existingPermanentFileKey;
            for (const payment of paymentDetails) {
                const receiptDetail = payment.receiptDetail;
                if ((receiptDetail === null || receiptDetail === void 0 ? void 0 : receiptDetail.fileKey) &&
                    receiptDetail.fileKey.startsWith('receipt/')) {
                    hasExistingPermanentFile = true;
                    existingPermanentFileKey = receiptDetail.fileKey;
                    break;
                }
            }
            // Log important information about file keys
            this.logger.log('File key analysis for payment document', {
                referenceAlphaId,
                action,
                hasPermanentFileKey,
                providedFileKey,
                existingPermanentFileKey,
                hasExistingPermanentFile
            });
            // Extract clinic details
            const clinicId = firstPayment.clinicId;
            const clinic = await this.clinicRepository.findOne({
                where: { id: clinicId },
                relations: ['brand']
            });
            if (!clinic) {
                this.logger.error('Clinic details not found', { clinicId });
                return;
            }
            // Calculate total amount
            const totalAmount = paymentDetails.reduce((sum, payment) => {
                // Include both cash amount and credit amount used
                const cashAmount = Number(payment.amount || 0);
                const creditAmount = Number(payment.creditAmountUsed || 0);
                return sum + cashAmount + creditAmount;
            }, 0);
            // Determine payment type
            const isRefund = firstPayment.type === 'Return' ||
                firstPayment.type === 'Credit Note' ||
                (firstPayment.type === 'Collect' && firstPayment.invoiceId);
            // Find credit related data based on payment type
            let creditsAdded = 0;
            if (!isRefund) {
                // For normal payments - find credits added
                const creditPayment = paymentDetails.find(payment => payment.type === 'Collect' && payment.isCreditsAdded);
                creditsAdded = creditPayment
                    ? Number(creditPayment.creditAmountAdded)
                    : 0;
            }
            else {
                // For refunds - we need to check if this is a credit debit (amount used with type Return)
                // For refunds, look for isCreditUsed = true which indicates credits were used/debited
                if (firstPayment.type === 'Collect' && firstPayment.invoiceId) {
                    // For Collect with invoiceId (refund case), check isCreditsAdded
                    const creditPayment = paymentDetails.find(payment => payment.isCreditsAdded);
                    creditsAdded = creditPayment
                        ? Number(creditPayment.creditAmountAdded || 0)
                        : 0;
                }
                else {
                    // For Return/Credit Note refunds, check isCreditUsed
                    const creditPayment = paymentDetails.find(payment => payment.type === 'Return' && payment.isCreditUsed);
                    if (creditPayment) {
                        creditsAdded = Number(creditPayment.creditAmountUsed || 0);
                    }
                }
                this.logger.log(`Refund credits debited: ${creditsAdded}`, {
                    referenceAlphaId
                });
            }
            // Handle clinic logo URL
            let clinicLogoUrl = (clinic === null || clinic === void 0 ? void 0 : clinic.logoUrl) || (clinic === null || clinic === void 0 ? void 0 : clinic.clinicLogo) || '';
            // Get pre-signed URL for clinic logo if it exists and is an S3 path
            if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
                try {
                    const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
                    clinicLogoUrl = logoPreSignedUrl;
                    this.logger.log('Generated pre-signed URL for clinic logo', logoPreSignedUrl);
                }
                catch (error) {
                    this.logger.error('Error generating pre-signed URL for clinic logo', error);
                }
            }
            // Extract pet name and owner last name for filename formatting
            let petName = 'pet';
            const ownerLastName = ((_a = firstPayment.ownerBrand) === null || _a === void 0 ? void 0 : _a.lastName) || 'owner';
            if (firstPayment.patientId) {
                const patient = await this.patientRepository.findOne({
                    where: { id: firstPayment.patientId }
                });
                if (patient) {
                    petName = patient.patientName || petName;
                }
            }
            // Format pet name and owner last name for filename (remove spaces, special chars)
            const formattedPetName = petName
                .replace(/[^a-zA-Z0-9]/g, '_')
                .toLowerCase();
            const formattedOwnerLastName = ownerLastName
                .replace(/[^a-zA-Z0-9]/g, '_')
                .toLowerCase();
            // For download filename format
            const fileNamePrefix = `${formattedPetName}_${formattedOwnerLastName}_receipt`;
            const fileName = `${fileNamePrefix}_${referenceAlphaId}.pdf`;
            // For email attachment format
            const emailAttachmentName = `RECEIPT_${referenceAlphaId}.pdf`;
            // Now determine the file handling approach based on file key availability
            let pdfBuffer;
            let fileKey;
            this.logger.log('Process payment document task params', {
                referenceAlphaId,
                action,
                shareMethod,
                hasPermanentFileKey,
                providedFileKey,
                hasExistingPermanentFile,
                existingPermanentFileKey
            });
            // First priority: use the provided permanent file key if available
            // Second priority: use existing permanent file from any payment detail's record
            // Last resort: generate a new file
            if (hasPermanentFileKey) {
                // Use the provided permanent file key
                this.logger.log('Using provided permanent file for payment request', {
                    fileKey: providedFileKey,
                    action,
                    referenceAlphaId
                });
                fileKey = providedFileKey;
            }
            else if (hasExistingPermanentFile) {
                // Use the existing permanent file key from the payment record
                this.logger.log('Using existing permanent file from payment record', {
                    fileKey: existingPermanentFileKey,
                    action,
                    referenceAlphaId
                });
                fileKey = existingPermanentFileKey;
            }
            else {
                // No permanent file available, generate a new PDF
                this.logger.log('No permanent file available, generating new payment receipt PDF', {
                    referenceAlphaId,
                    action,
                    providedFileKey
                });
                // Build outstanding invoices paid array (for Payment Receipt only)
                const outstandingInvoicesPaid = [];
                let referenceCreditNote = '';
                // Process data based on payment type
                if (!isRefund) {
                    // For normal payment receipts
                    for (const payment of paymentDetails) {
                        if (payment.invoice &&
                            payment.type !== 'Collect' &&
                            payment.invoice.referenceAlphaId) {
                            // Get patient name for this invoice
                            let petName = '';
                            if (payment.patientId) {
                                const patient = await this.patientService.findOne(payment.patientId);
                                if (patient) {
                                    petName = patient.patientName;
                                }
                            }
                            // Calculate the actual amount cleared for this invoice
                            const amountCleared = payment.paymentType === EnumPaymentType.Credits
                                ? Number(payment.creditAmountUsed || 0) // Use credit amount for credit payments
                                : Number(payment.amount || 0); // Use cash amount for cash payments
                            outstandingInvoicesPaid.push({
                                referenceInvoice: payment.invoice.referenceAlphaId,
                                invoiceAmountCleared: amountCleared,
                                pet: petName
                            });
                        }
                    }
                }
                else {
                    // For refund receipts, check for referenced credit note if available
                    if (firstPayment.invoice &&
                        firstPayment.invoice.referenceAlphaId) {
                        referenceCreditNote =
                            firstPayment.invoice.referenceAlphaId;
                    }
                }
                // Build the receipt data object
                const receiptData = isRefund
                    ? {
                        receiptNumber: referenceAlphaId,
                        receiptDate: moment(firstPayment.createdAt).format('MMMM D, YYYY'),
                        clinicName: clinic.name || '',
                        clinicAddress: this.getClinicAddress(clinic),
                        clinicPhone: ((_c = (_b = clinic.phoneNumbers) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.number) || '',
                        clinicEmail: clinic.email || '',
                        clinicWebsite: clinic.website || '',
                        customerName: `${((_d = firstPayment.ownerBrand) === null || _d === void 0 ? void 0 : _d.firstName) || ''} ${((_e = firstPayment.ownerBrand) === null || _e === void 0 ? void 0 : _e.lastName) || ''}`,
                        amount: totalAmount,
                        paymentType: firstPayment.paymentType || 'Cash',
                        clinicLogoUrl,
                        creditsAdded,
                        ReferenceCreditNote: referenceCreditNote
                    }
                    : {
                        receiptNumber: referenceAlphaId,
                        receiptDate: moment(firstPayment.createdAt).format('MMMM D, YYYY'),
                        clinicName: clinic.name || '',
                        clinicAddress: this.getClinicAddress(clinic),
                        clinicPhone: ((_g = (_f = clinic.phoneNumbers) === null || _f === void 0 ? void 0 : _f[0]) === null || _g === void 0 ? void 0 : _g.number) || '',
                        clinicEmail: clinic.email || '',
                        clinicWebsite: clinic.website || '',
                        customerName: `${((_h = firstPayment.ownerBrand) === null || _h === void 0 ? void 0 : _h.firstName) || ''} ${((_j = firstPayment.ownerBrand) === null || _j === void 0 ? void 0 : _j.lastName) || ''}`,
                        amount: totalAmount,
                        paymentType: firstPayment.paymentType || 'Cash',
                        clinicLogoUrl,
                        creditsAdded,
                        outstandingInvoicesPaid
                    };
                // Generate PDF from HTML based on payment type
                const pdfHtml = isRefund
                    ? (0, generateRefundReceipt_1.generateRefundReceipt)(receiptData)
                    : (0, generatePaymentReceipt_1.generatePaymentReceipt)(receiptData);
                pdfBuffer = await (0, generatePdf_1.generatePDF)(pdfHtml);
                // Create a temporary file key (using receipt_temp prefix, no .pdf extension)
                fileKey = `receipt_temp/${(0, uuidv7_1.uuidv4)()}`;
                // Upload PDF to S3
                await this.s3Service.uploadPdfToS3(pdfBuffer, fileKey);
                this.logger.log(`${isRefund ? 'Refund' : 'Payment'} Receipt PDF generated and uploaded to S3`, {
                    fileKey,
                    fileName,
                    referenceAlphaId,
                    userId
                });
                // Update all payment details with this reference ID to include the file key
                const receiptDetail = {
                    fileKey,
                    fileName,
                    fileType: 'PDF',
                    isGenerating: false
                };
                // Update all payment details records with the same receipt file
                const updatePromises = paymentDetails.map(payment => {
                    payment.receiptDetail = receiptDetail;
                    return this.paymentDetailsRepository.save(payment);
                });
                await Promise.all(updatePromises);
                this.logger.log('Updated all payment details with receipt file information', {
                    count: paymentDetails.length,
                    receiptDetail
                });
            }
            // For share action, we need to download the PDF buffer if we don't already have it
            if (action === 'share' &&
                (hasPermanentFileKey || hasExistingPermanentFile) &&
                !pdfBuffer) {
                try {
                    this.logger.log('Attempting to get pre-signed URL for permanent file to share', {
                        fileKey,
                        referenceAlphaId
                    });
                    const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
                    this.logger.log('Successfully got pre-signed URL', {
                        fileKey,
                        hasUrl: !!viewSignedUrl,
                        urlLength: (viewSignedUrl === null || viewSignedUrl === void 0 ? void 0 : viewSignedUrl.length) || 0
                    });
                    // Download the existing file
                    pdfBuffer = await this.downloadPDF(viewSignedUrl);
                    this.logger.log('Successfully downloaded permanent file for sharing', {
                        referenceAlphaId,
                        fileKey,
                        bufferSize: (pdfBuffer === null || pdfBuffer === void 0 ? void 0 : pdfBuffer.length) || 0
                    });
                }
                catch (error) {
                    // Do not regenerate - log the error and let the process fail
                    this.logger.error('Error retrieving permanent file for sharing - NOT regenerating', {
                        error: error instanceof Error
                            ? error.message
                            : String(error),
                        stack: error instanceof Error
                            ? error.stack
                            : undefined,
                        fileKey,
                        referenceAlphaId
                    });
                    // Exit without completing the task
                    return;
                }
            }
            // If this is a share action, handle sharing via email/WhatsApp
            if (action === 'share' && shareMethod) {
                // For share actions, we should have the PDF buffer by now
                if (!pdfBuffer) {
                    this.logger.error('Missing PDF buffer for sharing', {
                        referenceAlphaId,
                        fileKey,
                        action,
                        shareMethod,
                        recipient
                    });
                    return;
                }
                // Now we should have a buffer one way or another
                this.logger.log('About to share payment document', {
                    referenceAlphaId,
                    fileKey,
                    shareMethod,
                    recipient,
                    hasCustomEmail: !!customEmail,
                    hasCustomPhone: !!customPhoneNumber,
                    hasPdfBuffer: !!pdfBuffer,
                    bufferSize: (pdfBuffer === null || pdfBuffer === void 0 ? void 0 : pdfBuffer.length) || 0
                });
                // Handle sharing based on recipient type
                if (recipient === 'other' &&
                    (customEmail || customPhoneNumber)) {
                    // Custom recipient case
                    if ((shareMethod === 'email' || shareMethod === 'both') &&
                        customEmail) {
                        try {
                            const emailSubject = isRefund
                                ? `Refund Receipt ${referenceAlphaId} from ${clinic.name}`
                                : `Payment Receipt ${referenceAlphaId} from ${clinic.name}`;
                            const emailBody = `<p>Dear Recipient,</p>
							<p>Please find attached the ${isRefund ? 'refund' : 'payment'} receipt from ${clinic.name}.</p>
							<p>Thank you for choosing our services.</p>`;
                            // Send email with PDF attachment to custom email
                            await this.mailService.sendMail({
                                body: emailBody,
                                subject: emailSubject,
                                pdfBuffers: [pdfBuffer],
                                pdfFileNames: [emailAttachmentName],
                                toMailAddress: customEmail
                            });
                            this.logger.log(`${isRefund ? 'Refund' : 'Payment'} Receipt sent via email to custom recipient`, {
                                email: customEmail,
                                referenceAlphaId
                            });
                        }
                        catch (emailError) {
                            this.logger.error('Error sending receipt email to custom email', {
                                error: emailError instanceof Error
                                    ? emailError.message
                                    : String(emailError),
                                stack: emailError instanceof Error
                                    ? emailError.stack
                                    : undefined,
                                email: customEmail,
                                referenceAlphaId
                            });
                        }
                    }
                    // Handle custom WhatsApp
                    if ((shareMethod === 'whatsapp' ||
                        shareMethod === 'both') &&
                        customPhoneNumber) {
                        try {
                            // Get URL for viewing in WhatsApp
                            const viewUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
                            // Format the phone number to include country code if not already included
                            const formattedNumber = customPhoneNumber.startsWith('+')
                                ? customPhoneNumber.substring(1) // Remove '+' if present
                                : customPhoneNumber;
                            // Extract country code and number
                            // Note: This assumes Indian numbers (91) by default if no country code is present
                            let countryCode, phoneNumber;
                            if (formattedNumber.length > 10) {
                                // Assume country code is present
                                countryCode = formattedNumber.substring(0, formattedNumber.length - 10);
                                phoneNumber = formattedNumber.substring(formattedNumber.length - 10);
                            }
                            else {
                                // Default to India country code if not specified
                                countryCode = '91';
                                phoneNumber = formattedNumber;
                            }
                            const mobileNumber = `${countryCode}${phoneNumber}`;
                            // Get formatted date for receipt
                            const receiptDate = moment(firstPayment.createdAt).format('MMMM D, YYYY');
                            // Generate template data
                            const templateArgs = {
                                clientName: 'Recipient',
                                receiptDate,
                                brandName: clinic.name,
                                receiptFile: viewUrl,
                                mobileNumber
                            };
                            const templateData = (0, template_helper_util_1.selectTemplate)(clinic, templateArgs, whatsapp_template_generator_3.getReceiptGenerationTemplateData, whatsapp_template_generator_3.getReceiptGenerationClinicLinkTemplateData);
                            this.logger.log(`${isRefund ? 'Refund' : 'Payment'} Receipt sent via WhatsApp successfully to custom number`, {
                                phoneNumber: mobileNumber,
                                referenceAlphaId,
                                templateName: templateData.templateName
                            });
                        }
                        catch (whatsappError) {
                            this.logger.error('Error sending WhatsApp to custom number', {
                                error: whatsappError instanceof Error
                                    ? whatsappError.message
                                    : String(whatsappError),
                                stack: whatsappError instanceof Error
                                    ? whatsappError.stack
                                    : undefined,
                                phoneNumber: customPhoneNumber,
                                referenceAlphaId
                            });
                        }
                    }
                }
                else {
                    // Default client recipient case - use owner information
                    if (shareMethod === 'email' || shareMethod === 'both') {
                        // Collect all unique owners
                        const uniqueOwners = new Map();
                        // First, add owners directly from payment details
                        for (const payment of paymentDetails) {
                            if (payment.ownerBrand && payment.ownerBrand.id) {
                                uniqueOwners.set(payment.ownerBrand.id, payment.ownerBrand);
                            }
                        }
                        // Then, if any payment has a patientId, get all owners from the patient
                        for (const payment of paymentDetails) {
                            if (payment.patientId) {
                                const patientDetails = await this.patientRepository.findOne({
                                    where: { id: payment.patientId },
                                    relations: [
                                        'patientOwners',
                                        'patientOwners.ownerBrand'
                                    ]
                                });
                                if (patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners) {
                                    for (const patientOwner of patientDetails.patientOwners) {
                                        if (patientOwner.ownerBrand &&
                                            patientOwner.ownerBrand.id) {
                                            uniqueOwners.set(patientOwner.ownerBrand.id, patientOwner.ownerBrand);
                                        }
                                    }
                                }
                                // Only need to check one payment with a patientId
                                break;
                            }
                        }
                        this.logger.log('Found unique owners for email sending', {
                            count: uniqueOwners.size,
                            referenceAlphaId
                        });
                        // Send email to each unique owner with an email address
                        for (const owner of uniqueOwners.values()) {
                            if (!owner.email) {
                                this.logger.warn('Owner has no email address, skipping email', {
                                    ownerId: owner.id,
                                    referenceAlphaId
                                });
                                continue;
                            }
                            try {
                                const emailSubject = isRefund
                                    ? `Refund Receipt ${referenceAlphaId} from ${clinic.name}`
                                    : `Payment Receipt ${referenceAlphaId} from ${clinic.name}`;
                                const emailBody = `<p>Dear ${owner.firstName || 'Recipient'},</p>
								<p>Please find attached the ${isRefund ? 'refund' : 'payment'} receipt from ${clinic.name}.</p>
								<p>Thank you for choosing our services.</p>`;
                                // Send email with PDF attachment
                                await this.mailService.sendMail({
                                    body: emailBody,
                                    subject: emailSubject,
                                    pdfBuffers: [pdfBuffer],
                                    pdfFileNames: [emailAttachmentName],
                                    toMailAddress: owner.email
                                });
                                this.logger.log(`${isRefund ? 'Refund' : 'Payment'} Receipt sent via email to ${owner.firstName} ${owner.lastName}`, {
                                    email: owner.email,
                                    ownerId: owner.id,
                                    referenceAlphaId
                                });
                            }
                            catch (emailError) {
                                this.logger.error('Error sending receipt email to owner', {
                                    error: emailError instanceof Error
                                        ? emailError.message
                                        : String(emailError),
                                    stack: emailError instanceof Error
                                        ? emailError.stack
                                        : undefined,
                                    ownerId: owner.id,
                                    email: owner.email,
                                    referenceAlphaId
                                });
                                // Continue with next owner even if one fails
                            }
                        }
                    }
                    if (shareMethod === 'whatsapp' || shareMethod === 'both') {
                        // Get URL for viewing in WhatsApp
                        const viewUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
                        // Collect all unique owners (same approach as email section)
                        const uniqueOwners = new Map();
                        // First, add owners directly from payment details
                        for (const payment of paymentDetails) {
                            if (payment.ownerBrand && payment.ownerBrand.id) {
                                uniqueOwners.set(payment.ownerBrand.id, payment.ownerBrand);
                            }
                        }
                        // Then, if any payment has a patientId, get all owners from the patient
                        for (const payment of paymentDetails) {
                            if (payment.patientId) {
                                const patientDetails = await this.patientRepository.findOne({
                                    where: { id: payment.patientId },
                                    relations: [
                                        'patientOwners',
                                        'patientOwners.ownerBrand',
                                        'patientOwners.ownerBrand.globalOwner'
                                    ]
                                });
                                if (patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners) {
                                    for (const patientOwner of patientDetails.patientOwners) {
                                        if (patientOwner.ownerBrand &&
                                            patientOwner.ownerBrand.id) {
                                            uniqueOwners.set(patientOwner.ownerBrand.id, patientOwner.ownerBrand);
                                        }
                                    }
                                }
                                // Only need to check one payment with a patientId
                                break;
                            }
                        }
                        this.logger.log('Found unique owners for WhatsApp sending', {
                            count: uniqueOwners.size,
                            referenceAlphaId
                        });
                        // Loop through all unique owners and send to each one with a phone number
                        for (const owner of uniqueOwners.values()) {
                            // For WhatsApp, we need to load globalOwner data if not already loaded
                            if (!owner.globalOwner) {
                                try {
                                    // Load the owner with globalOwner relation if not already loaded
                                    const ownerWithGlobalOwner = await this.ownerBrandRepository.findOne({
                                        where: { id: owner.id },
                                        relations: ['globalOwner']
                                    });
                                    if (ownerWithGlobalOwner &&
                                        ownerWithGlobalOwner.globalOwner) {
                                        owner.globalOwner =
                                            ownerWithGlobalOwner.globalOwner;
                                    }
                                }
                                catch (error) {
                                    this.logger.error('Error loading globalOwner data', {
                                        ownerId: owner.id,
                                        error: error instanceof Error
                                            ? error.message
                                            : String(error)
                                    });
                                }
                            }
                            if (!((_k = owner.globalOwner) === null || _k === void 0 ? void 0 : _k.phoneNumber) ||
                                !((_l = owner.globalOwner) === null || _l === void 0 ? void 0 : _l.countryCode)) {
                                this.logger.warn('Owner has no phone number, skipping WhatsApp', {
                                    ownerId: owner.id,
                                    referenceAlphaId
                                });
                                continue;
                            }
                            try {
                                const clientName = `${owner.firstName || ''} ${owner.lastName || ''}`;
                                const mobileNumber = `${owner.globalOwner.countryCode}${owner.globalOwner.phoneNumber}`;
                                // Get formatted date for receipt
                                const receiptDate = moment(firstPayment.createdAt).format('MMMM D, YYYY');
                                // Generate template data
                                const templateArgs = {
                                    clientName,
                                    receiptDate,
                                    brandName: clinic.name,
                                    receiptFile: viewUrl,
                                    mobileNumber
                                };
                                const templateData = (0, template_helper_util_1.selectTemplate)(clinic, templateArgs, whatsapp_template_generator_3.getReceiptGenerationTemplateData, whatsapp_template_generator_3.getReceiptGenerationClinicLinkTemplateData);
                                // Send WhatsApp message using template
                                await this.whatsappService.sendTemplateMessage({
                                    templateName: templateData.templateName,
                                    valuesArray: templateData.valuesArray,
                                    mobileNumber: templateData.mobileNumber
                                });
                                this.logger.log(`${isRefund ? 'Refund' : 'Payment'} Receipt sent via WhatsApp successfully to ${clientName}`, {
                                    phoneNumber: mobileNumber,
                                    referenceAlphaId,
                                    templateName: templateData.templateName
                                });
                            }
                            catch (whatsappError) {
                                this.logger.error('Error sending WhatsApp to specific owner', {
                                    error: whatsappError instanceof Error
                                        ? whatsappError.message
                                        : String(whatsappError),
                                    stack: whatsappError instanceof Error
                                        ? whatsappError.stack
                                        : undefined,
                                    ownerId: owner.id,
                                    referenceAlphaId
                                });
                                // Continue with next owner even if one fails
                            }
                        }
                    }
                    this.logger.log('Successfully shared payment document', {
                        referenceAlphaId,
                        fileKey,
                        shareMethod,
                        recipient
                    });
                }
                this.logger.log('Payment document task completed successfully', {
                    referenceAlphaId,
                    action,
                    fileKey,
                    recipient
                });
            }
        }
        catch (error) {
            // Enhanced error logging with detailed information
            this.logger.error('Error processing payment document', {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                data: {
                    referenceAlphaId: data === null || data === void 0 ? void 0 : data.referenceAlphaId,
                    action: data === null || data === void 0 ? void 0 : data.action,
                    shareMethod: data === null || data === void 0 ? void 0 : data.shareMethod,
                    recipient: data === null || data === void 0 ? void 0 : data.recipient
                }
            });
        }
    }
    // Update patient details for euthanasia and neuteredSpayed services/medications
    async updatePatientDetails(data) {
        var _a, _b;
        try {
            const { patientId, invoiceId, appointmentId, euthanasiaItems = [], neuteredSpayedItems = [], clinicId, brandId, userId } = data;
            this.logger.log('Processing patient detail update task', {
                patientId,
                invoiceId,
                appointmentId,
                euthanasiaItemsCount: euthanasiaItems.length,
                neuteredSpayedItemsCount: neuteredSpayedItems.length,
                clinicId,
                brandId,
                userId
            });
            // Check if we have any relevant items to process
            if (euthanasiaItems.length === 0 &&
                neuteredSpayedItems.length === 0) {
                this.logger.warn('No relevant items found for patient detail update', {
                    patientId,
                    invoiceId
                });
                return;
            }
            // Validate userId is provided
            if (!userId) {
                this.logger.error('No user ID provided for patient detail update', {
                    invoiceId,
                    patientId
                });
                return;
            }
            // Call the patient service to process the updates
            const result = await this.patientService.processPatientDetailUpdates(patientId, euthanasiaItems, neuteredSpayedItems, userId, invoiceId);
            if (result.success) {
                this.logger.log('Patient detail update completed successfully', {
                    patientId,
                    invoiceId,
                    userId,
                    euthanasiaItemsCount: euthanasiaItems.length,
                    neuteredSpayedItemsCount: neuteredSpayedItems.length
                });
            }
            else {
                this.logger.error('Patient detail update failed', {
                    patientId,
                    invoiceId,
                    userId,
                    errors: result.errors
                });
            }
        }
        catch (error) {
            this.logger.error('Error processing patient detail update task', {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                data: {
                    patientId: data === null || data === void 0 ? void 0 : data.patientId,
                    invoiceId: data === null || data === void 0 ? void 0 : data.invoiceId,
                    euthanasiaItemsCount: ((_a = data === null || data === void 0 ? void 0 : data.euthanasiaItems) === null || _a === void 0 ? void 0 : _a.length) || 0,
                    neuteredSpayedItemsCount: ((_b = data === null || data === void 0 ? void 0 : data.neuteredSpayedItems) === null || _b === void 0 ? void 0 : _b.length) || 0
                }
            });
            throw error;
        }
    }
    // Update inventory for invoice updates
    async updateInventoryForInvoiceUpdate(data) {
        var _a;
        try {
            const { invoiceId, inventoryChanges, userId } = data;
            this.logger.log('Processing inventory updates for invoice update', {
                invoiceId,
                changesCount: (inventoryChanges === null || inventoryChanges === void 0 ? void 0 : inventoryChanges.length) || 0,
                userId
            });
            if (!inventoryChanges || inventoryChanges.length === 0) {
                this.logger.log('No inventory changes to process', {
                    invoiceId
                });
                return;
            }
            // Process each inventory change
            for (const change of inventoryChanges) {
                try {
                    const { action, itemType, itemId, quantity } = change;
                    this.logger.log('Processing inventory change', {
                        invoiceId,
                        action,
                        itemType,
                        itemId,
                        quantity
                    });
                    // Get the appropriate repository based on item type
                    let repository;
                    switch (itemType) {
                        case 'Product':
                            repository = this.clinicProduct;
                            break;
                        case 'Vaccination':
                            repository = this.clinicVaccination;
                            break;
                        case 'Medication':
                            repository = this.clinicMedication;
                            break;
                        case 'Consumable':
                            repository = this.clinicConsumableEntity;
                            break;
                        default:
                            this.logger.warn('Unknown item type for inventory update', {
                                itemType,
                                itemId,
                                invoiceId
                            });
                            continue;
                    }
                    // Find the inventory item
                    const inventoryItem = await repository.findOne({
                        where: { id: itemId }
                    });
                    if (!inventoryItem) {
                        this.logger.warn('Inventory item not found', {
                            itemType,
                            itemId,
                            invoiceId
                        });
                        continue;
                    }
                    // Apply the inventory change
                    if (action === 'revert') {
                        // Add back to stock (item was removed from invoice)
                        inventoryItem.currentStock =
                            (inventoryItem.currentStock || 0) + quantity;
                    }
                    else if (action === 'deduct') {
                        // Remove from stock (item was added to invoice)
                        inventoryItem.currentStock = Math.max(0, (inventoryItem.currentStock || 0) - quantity);
                    }
                    // Save the updated inventory
                    switch (itemType) {
                        case 'Product':
                            await this.clinicProduct.save(inventoryItem);
                            break;
                        case 'Vaccination':
                            await this.clinicVaccination.save(inventoryItem);
                            break;
                        case 'Medication':
                            await this.clinicMedication.save(inventoryItem);
                            break;
                        case 'Consumable':
                            await this.clinicConsumableEntity.save(inventoryItem);
                            break;
                    }
                    this.logger.log('Inventory updated successfully', {
                        itemType,
                        itemId,
                        action,
                        quantity,
                        newStock: inventoryItem.currentStock
                    });
                }
                catch (error) {
                    this.logger.error('Error processing individual inventory change', {
                        error: error instanceof Error
                            ? error.message
                            : String(error),
                        change,
                        invoiceId
                    });
                    // Continue processing other changes even if one fails
                }
            }
            this.logger.log('Completed inventory updates for invoice update', {
                invoiceId,
                totalChanges: inventoryChanges.length
            });
        }
        catch (error) {
            this.logger.error('Error updating inventory for invoice update', {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                invoiceId: data === null || data === void 0 ? void 0 : data.invoiceId,
                changesCount: ((_a = data === null || data === void 0 ? void 0 : data.inventoryChanges) === null || _a === void 0 ? void 0 : _a.length) || 0
            });
            throw error;
        }
    }
};
exports.ProcessInvoiceTasksHandler = ProcessInvoiceTasksHandler;
exports.ProcessInvoiceTasksHandler = ProcessInvoiceTasksHandler = __decorate([
    (0, common_1.Injectable)(),
    __param(4, (0, typeorm_1.InjectRepository)(patient_vaccinations_entity_1.PatientVaccination)),
    __param(5, (0, typeorm_1.InjectRepository)(patient_entity_1.Patient)),
    __param(6, (0, typeorm_1.InjectRepository)(clinic_product_entity_1.ClinicProductEntity)),
    __param(7, (0, typeorm_1.InjectRepository)(clinic_vaccination_entity_1.ClinicVaccinationEntity)),
    __param(8, (0, typeorm_1.InjectRepository)(clinic_medication_entity_1.ClinicMedicationEntity)),
    __param(9, (0, typeorm_1.InjectRepository)(clinic_consumable_entity_1.ClinicConsumableEntity)),
    __param(10, (0, typeorm_1.InjectRepository)(invoice_entity_1.InvoiceEntity)),
    __param(11, (0, typeorm_1.InjectRepository)(payment_details_entity_1.PaymentDetailsEntity)),
    __param(12, (0, typeorm_1.InjectRepository)(clinic_entity_1.ClinicEntity)),
    __param(13, (0, typeorm_1.InjectRepository)(owner_brand_entity_1.OwnerBrand)),
    __param(18, (0, typeorm_1.InjectRepository)(appointment_details_entity_1.AppointmentDetailsEntity)),
    __param(19, (0, typeorm_1.InjectRepository)(appointment_doctor_entity_1.AppointmentDoctorsEntity)),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        s3_service_1.S3Service,
        send_mail_service_1.SESMailService,
        whatsapp_service_1.WhatsappService,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        patient_reminder_service_1.PatientRemindersService,
        global_reminders_service_1.GlobalReminderService,
        patients_service_1.PatientsService,
        clinic_lab_report_service_1.ClinicLabReportService,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ProcessInvoiceTasksHandler);
//# sourceMappingURL=process_invoice_tasks.handler.js.map