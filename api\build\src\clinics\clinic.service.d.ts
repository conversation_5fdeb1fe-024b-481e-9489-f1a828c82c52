import { CreateClinicDto, UpdateBasicClinicDto } from './dto/create-clinic.dto';
import { ClinicEntity } from './entities/clinic.entity';
import { DataSource, Repository } from 'typeorm';
import { ClinicRoomEntity } from './entities/clinic-room.entity';
import { UpdateClinicDto } from './dto/update-clinic.dto';
import { ClinicConsumblesService } from '../clinic-consumables/clinic-consumbles.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicMedicationsService } from '../clinic-medications/clinic-medications.service';
import { ClinicProductsService } from '../clinic-products/clinic-products.service';
import { ClinicServicesService } from '../clinic-services/clinic-services.service';
import { ClinicVaccinationsService } from '../clinic-vaccinations/clinic-vaccinations.service';
import { ClinicLabReportService } from '../clinic-lab-report/clinic-lab-report.service';
import { UsersService } from '../users/users.service';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { ClinicUser } from './entities/clinic-user.entity';
import { CreateClinicRoomDto, UpdateClinicRoomDto } from './dto/create-clinic-room.dto';
import { BrandService } from '../brands/brands.service';
import { UpdateClientBookingSettingsDto } from './dto/update-client-booking-settings.dto';
import { ClientBookingSettingsResponseDto } from './dto/client-booking-settings-response.dto';
import { ClinicSettingsDto, ClinicSettingsResponseDto } from './dto/clinic-settings.dto';
export declare class ClinicService {
    private clinicRepository;
    private clinicRoomRepository;
    private clinicUserRepository;
    private readonly logger;
    private readonly consumblesService;
    private readonly clinicMedicationsService;
    private readonly productsService;
    private readonly clinicServices;
    private readonly vaccinationService;
    private readonly clinicLabReportService;
    private userService;
    private readonly mailService;
    private dataSource;
    private brandService;
    constructor(clinicRepository: Repository<ClinicEntity>, clinicRoomRepository: Repository<ClinicRoomEntity>, clinicUserRepository: Repository<ClinicUser>, logger: WinstonLogger, consumblesService: ClinicConsumblesService, clinicMedicationsService: ClinicMedicationsService, productsService: ClinicProductsService, clinicServices: ClinicServicesService, vaccinationService: ClinicVaccinationsService, clinicLabReportService: ClinicLabReportService, userService: UsersService, mailService: SESMailService, dataSource: DataSource, brandService: BrandService);
    createClinic(createClinicDto: CreateClinicDto, createdBy: string): Promise<ClinicEntity>;
    getAllClinics(page?: number, limit?: number, orderBy?: string): Promise<{
        clinics: ClinicEntity[];
        total: number;
    }>;
    updateBasicClinicInfo(id: string, updateBasicClinicDto: UpdateBasicClinicDto, userId: string): Promise<ClinicEntity>;
    updateClinic(id: string, updateClinicDto: UpdateClinicDto, userId: string): Promise<ClinicEntity>;
    getClinicById(id: string): Promise<ClinicEntity>;
    getClinicRooms(id: string): Promise<{
        rooms: ClinicRoomEntity[];
        total: number;
    }>;
    createClinicRoom(createClinicRoomDto: CreateClinicRoomDto, brandId: string): Promise<ClinicRoomEntity>;
    updateClinicRoom(id: string, updateClinicRoomDto: UpdateClinicRoomDto): Promise<ClinicRoomEntity>;
    deleteRoom(id: string): Promise<void>;
    deactivateClinic(id: string): Promise<ClinicEntity>;
    reactivateClinic(id: string): Promise<ClinicEntity>;
    softDeleteClinic(id: string): Promise<ClinicEntity>;
    processBulkUpload(file: Express.Multer.File, clinicId: string, brandId: string): Promise<{
        [sheetName: string]: any;
    }>;
    private processMedications;
    private processConsumables;
    private processVaccinations;
    private processServices;
    private processProducts;
    private processDiagnostics;
    private processItems;
    generateInventoryExcel(clinicId: string): Promise<Buffer>;
    deleteInventoryItem(itemType: string, itemId: string): Promise<{
        message: string;
    }>;
    getClientBookingSettings(clinicId: string): Promise<ClientBookingSettingsResponseDto | null>;
    updateClientBookingSettings(clinicId: string, dto: UpdateClientBookingSettingsDto, updatedBy: string): Promise<ClinicEntity>;
    getClinicSettings(clinicId: string): Promise<ClinicSettingsResponseDto>;
    updateClinicSettings(clinicId: string, settingsDto: ClinicSettingsDto, updatedBy: string): Promise<ClinicSettingsResponseDto>;
}
