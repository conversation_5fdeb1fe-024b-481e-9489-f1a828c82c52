"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
const session_service_1 = require("../../session/session.service");
const role_enum_1 = require("../../roles/role.enum");
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(configService, sessionService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_SECRET')
        });
        this.configService = configService;
        this.sessionService = sessionService;
    }
    async validate(payload) {
        // Enforce single session for all roles except SUPER_ADMIN
        if ((payload === null || payload === void 0 ? void 0 : payload.sid) && (payload === null || payload === void 0 ? void 0 : payload.role) !== role_enum_1.Role.SUPER_ADMIN) {
            const currentSid = await this.sessionService.getUserSession(payload.sub);
            if (currentSid && currentSid !== payload.sid) {
                throw new common_1.UnauthorizedException('SESSION_ENDED');
            }
        }
        return {
            id: payload.sub,
            email: payload.email,
            roleId: payload.role,
            owner: payload.owner,
            userId: payload.sub,
            role: payload.role,
            clinicId: payload.clinicId,
            brandId: payload.brandId
        };
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        session_service_1.SessionService])
], JwtStrategy);
//# sourceMappingURL=jwt-auth.strategy.js.map