"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicController = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars */
const common_1 = require("@nestjs/common");
const clinic_service_1 = require("./clinic.service");
const create_clinic_dto_1 = require("./dto/create-clinic.dto");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const swagger_1 = require("@nestjs/swagger");
const api_documentation_base_1 = require("../base/api-documentation-base");
const clinic_entity_1 = require("./entities/clinic.entity");
const clinic_room_entity_1 = require("./entities/clinic-room.entity");
const update_clinic_dto_1 = require("./dto/update-clinic.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const platform_express_1 = require("@nestjs/platform-express");
const create_clinic_room_dto_1 = require("./dto/create-clinic-room.dto");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const update_client_booking_settings_dto_1 = require("./dto/update-client-booking-settings.dto");
const client_booking_settings_response_dto_1 = require("./dto/client-booking-settings-response.dto");
const clinic_settings_dto_1 = require("./dto/clinic-settings.dto");
let ClinicController = class ClinicController extends api_documentation_base_1.ApiDocumentationBase {
    constructor(logger, clinicService) {
        super();
        this.logger = logger;
        this.clinicService = clinicService;
    }
    async createClinic(createClinicDto, req) {
        try {
            this.logger.log('Creating new clinic', { dto: createClinicDto });
            const user = req.user;
            return await this.clinicService.createClinic(createClinicDto, user.userId);
        }
        catch (error) {
            this.logger.error('Error creating new clinic', {
                error,
                createClinicDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getClinicById(id) {
        try {
            this.logger.log('Fetching clinic by ID', { id });
            return await this.clinicService.getClinicById(id);
        }
        catch (error) {
            this.logger.error('Error fetching clinic by ID', { error });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
        }
    }
    async updateBasicClinic(id, updateBasicClinicDto, req) {
        const user = req.user;
        return this.clinicService.updateBasicClinicInfo(id, updateBasicClinicDto, user.userId);
    }
    async deactivateClinic(id) {
        try {
            this.logger.log('Deactivating clinic', { id });
            return await this.clinicService.deactivateClinic(id);
        }
        catch (error) {
            this.logger.error('Error deactivating clinic', { error });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async reactivateClinic(id) {
        try {
            this.logger.log('Reactivating clinic', { id });
            return await this.clinicService.reactivateClinic(id);
        }
        catch (error) {
            this.logger.error('Error reactivating clinic', { error });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async softDeleteClinic(id) {
        try {
            this.logger.log('Soft deleting clinic', { id });
            return await this.clinicService.softDeleteClinic(id);
        }
        catch (error) {
            this.logger.error('Error soft deleting clinic', { error });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async updateClinic(id, updateClinicDto, req) {
        const user = req.user;
        return this.clinicService.updateClinic(id, updateClinicDto, user.userId);
    }
    async getAllClinics(page = 1, limit = 10, orderBy = 'DESC') {
        try {
            this.logger.log('Fetching all clinics', {
                page,
                limit,
                orderBy
            });
            return await this.clinicService.getAllClinics(page, limit, orderBy);
        }
        catch (error) {
            this.logger.error('Error fetching clinics', { error });
            throw new common_1.HttpException('Error fetching all the clinics', common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getClinicRooms(id) {
        try {
            this.logger.log('Fetching all clinic rooms', { id });
            return await this.clinicService.getClinicRooms(id);
        }
        catch (error) {
            this.logger.error('Error fetching clinic rooms', { error });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async createClinicRoom(createClinicRoomDto, req) {
        try {
            this.logger.log('Creating new clinic room', {
                dto: createClinicRoomDto
            });
            const room = await this.clinicService.createClinicRoom(createClinicRoomDto, req.user.brandId);
            this.logger.log('Clinic room created successfully', {
                roomId: room.id
            });
            return room;
        }
        catch (error) {
            this.logger.error('Error creating clinic room', { error });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async updateClinicRoom(id, updateClinicRoomDto) {
        try {
            this.logger.log('Updating clinic room', {
                id,
                dto: updateClinicRoomDto
            });
            const room = await this.clinicService.updateClinicRoom(id, updateClinicRoomDto);
            this.logger.log('Clinic room updated successfully', {
                roomId: room.id
            });
            return room;
        }
        catch (error) {
            this.logger.error('Error updating clinic room', { error });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async deleteRoom(id) {
        try {
            await this.clinicService.deleteRoom(id);
            return { message: 'Room deleted successfully' };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to delete room', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async uploadFile(file, clinicId, brandId, request) {
        if (!file) {
            throw new common_1.HttpException('No file uploaded', common_1.HttpStatus.BAD_REQUEST);
        }
        if (!clinicId || !brandId) {
            throw new common_1.HttpException('Clinic ID and Brand ID are required', common_1.HttpStatus.BAD_REQUEST);
        }
        // const userId = (request.user as any)?.id;
        // if (!userId) {
        //   throw new HttpException('User not authenticated', HttpStatus.UNAUTHORIZED);
        // }
        try {
            this.logger.log('Starting bulk Excel file processing');
            const result = await this.clinicService.processBulkUpload(file, clinicId, brandId);
            return result;
        }
        catch (error) {
            this.logger.error('Error processing bulk Excel file', { error });
            throw new common_1.HttpException('Error processing bulk Excel file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async downloadInventory(clinicId, res) {
        const buffer = await this.clinicService.generateInventoryExcel(clinicId);
        res.set({
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition': `attachment; filename="inventory_${clinicId}.xlsx"`,
            'Content-Length': buffer.length
        });
        res.end(buffer);
    }
    async deleteInventoryItem(itemType, itemId) {
        return await this.clinicService.deleteInventoryItem(itemType, itemId);
    }
    // @Get(':clinicId/working-hours')
    // @Roles(Role.ADMIN, Role.SUPER_ADMIN)
    // @ApiOperation({ summary: 'Get clinic working hours' })
    // @ApiResponse({
    // 	status: 200,
    // 	description: 'Returns the working hours',
    // 	type: WorkingHoursDto
    // })
    // async getWorkingHours(@Param('clinicId') clinicId: string) {
    // 	return this.clinicService.getWorkingHours(clinicId);
    // }
    // @Put(':clinicId/working-hours')
    // @Roles(Role.ADMIN, Role.SUPER_ADMIN)
    // @ApiOperation({ summary: 'Update clinic working hours' })
    // @ApiResponse({
    // 	status: 200,
    // 	description: 'Working hours updated successfully',
    // 	type: WorkingHoursDto
    // })
    // async updateWorkingHours(
    // 	@Param('clinicId') clinicId: string,
    // 	@Body() workingHoursDto: WorkingHoursDto
    // ) {
    // 	return this.clinicService.updateWorkingHours(clinicId, workingHoursDto);
    // }
    // Endpoints for Client Booking Settings
    async getClientBookingSettings(id) {
        try {
            this.logger.log('Getting client booking settings', {
                clinicId: id
            });
            const settings = await this.clinicService.getClientBookingSettings(id);
            // Service method throws NotFoundException if clinic doesn't exist
            return settings;
        }
        catch (error) {
            this.logger.error('Error getting client booking settings', {
                clinicId: id,
                error
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to retrieve client booking settings', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateClientBookingSettings(id, dto, req // Use standard Request type
    ) {
        try {
            const user = req.user; // Extract userId from JWT payload
            if (!user || !user.userId) {
                // This should ideally be caught by AuthGuard, but double-check
                throw new common_1.HttpException('Unauthorized', common_1.HttpStatus.UNAUTHORIZED);
            }
            this.logger.log('Updating client booking settings', {
                clinicId: id,
                dto,
                updatedBy: user.userId
            });
            return await this.clinicService.updateClientBookingSettings(id, dto, user.userId);
        }
        catch (error) {
            this.logger.error('Error updating client booking settings', {
                clinicId: id,
                dto,
                error
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to update client booking settings', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    // Endpoints for Clinic Settings
    async getClinicSettings(id) {
        try {
            this.logger.log('Getting clinic settings', {
                clinicId: id
            });
            const settings = await this.clinicService.getClinicSettings(id);
            return settings;
        }
        catch (error) {
            this.logger.error('Error getting clinic settings', {
                clinicId: id,
                error
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to retrieve clinic settings', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateClinicSettings(id, dto, req) {
        try {
            const user = req.user;
            if (!user || !user.userId) {
                throw new common_1.HttpException('Unauthorized', common_1.HttpStatus.UNAUTHORIZED);
            }
            this.logger.log('Updating clinic settings', {
                clinicId: id,
                dto,
                updatedBy: user.userId
            });
            return await this.clinicService.updateClinicSettings(id, dto, user.userId);
        }
        catch (error) {
            this.logger.error('Error updating clinic settings', {
                clinicId: id,
                dto,
                error
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to update clinic settings', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.ClinicController = ClinicController;
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Creates a new clinic',
        type: clinic_entity_1.ClinicEntity
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, common_1.Post)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe()),
    (0, track_method_decorator_1.TrackMethod)('createClinic-clinics'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_clinic_dto_1.CreateClinicDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "createClinic", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the clinic with the given ID',
        type: clinic_entity_1.ClinicEntity
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.Get)(':id'),
    (0, track_method_decorator_1.TrackMethod)('getClinicById-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "getClinicById", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Updates the Basic clinic Deatils with the given ID',
        type: clinic_entity_1.ClinicEntity
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, common_1.Put)('/basic/:id'),
    (0, track_method_decorator_1.TrackMethod)('updateBasicClinic-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_clinic_dto_1.UpdateBasicClinicDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "updateBasicClinic", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Deactivate the Clinic using the Id'
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, common_1.Put)(':id/deactivate'),
    (0, track_method_decorator_1.TrackMethod)('deactivateClinic-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "deactivateClinic", null);
__decorate([
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, common_1.Put)(':id/reactivate'),
    (0, track_method_decorator_1.TrackMethod)('reactivateClinic-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "reactivateClinic", null);
__decorate([
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, common_1.Delete)(':id'),
    (0, track_method_decorator_1.TrackMethod)('softDeleteClinic-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "softDeleteClinic", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Updates the clinic with the given ID',
        type: clinic_entity_1.ClinicEntity
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.Put)(':id'),
    (0, track_method_decorator_1.TrackMethod)('updateClinic-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_clinic_dto_1.UpdateClinicDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "updateClinic", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the list of clinics',
        isArray: true,
        type: clinic_entity_1.ClinicEntity
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.Get)(),
    (0, track_method_decorator_1.TrackMethod)('getAllClinics-clinics'),
    __param(0, (0, common_1.Query)('page', new common_1.DefaultValuePipe(1), common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('limit', new common_1.DefaultValuePipe(10), common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('orderBy', new common_1.DefaultValuePipe('DESC'))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "getAllClinics", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the list of clinic rooms',
        isArray: true,
        type: clinic_room_entity_1.ClinicRoomEntity
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.Get)(':id/rooms'),
    (0, track_method_decorator_1.TrackMethod)('getClinicRooms-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "getClinicRooms", null);
__decorate([
    (0, common_1.Post)('rooms'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOkResponse)({
        description: 'The clinic room has been successfully created.',
        type: clinic_room_entity_1.ClinicRoomEntity
    }),
    (0, track_method_decorator_1.TrackMethod)('createClinicRoom-clinics'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_clinic_room_dto_1.CreateClinicRoomDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "createClinicRoom", null);
__decorate([
    (0, common_1.Put)('rooms/:id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOkResponse)({
        description: 'The clinic room has been successfully updated.',
        type: clinic_room_entity_1.ClinicRoomEntity
    }),
    (0, track_method_decorator_1.TrackMethod)('updateClinicRoom-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_clinic_room_dto_1.UpdateClinicRoomDto]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "updateClinicRoom", null);
__decorate([
    (0, common_1.Delete)('rooms/:id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Room deleted successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Room not found.' }),
    (0, track_method_decorator_1.TrackMethod)('deleteRoom-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "deleteRoom", null);
__decorate([
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.Post)('bulk-upload'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    (0, track_method_decorator_1.TrackMethod)('uploadFile-clinics'),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Query)('clinicId')),
    __param(2, (0, common_1.Query)('brandId')),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, Object]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Get)('inventory/download/:clinicId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('downloadInventory-clinics'),
    __param(0, (0, common_1.Param)('clinicId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "downloadInventory", null);
__decorate([
    (0, common_1.Delete)('/inventory/:itemType/:itemId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('deleteInventoryItem-clinics'),
    __param(0, (0, common_1.Param)('itemType')),
    __param(1, (0, common_1.Param)('itemId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "deleteInventoryItem", null);
__decorate([
    (0, common_1.Get)(':id/client-booking-settings'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.LAB_TECHNICIAN) // Allow Super Admin and Clinic Admin
    ,
    (0, swagger_1.ApiOperation)({ summary: 'Get client booking settings for a clinic' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the client booking settings including allowed doctor names.',
        type: client_booking_settings_response_dto_1.ClientBookingSettingsResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Clinic not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('getClientBookingSettings-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "getClientBookingSettings", null);
__decorate([
    (0, common_1.Put)(':id/client-booking-settings'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.LAB_TECHNICIAN) // Allow Super Admin and Clinic Admin
    ,
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, skipMissingProperties: true })),
    (0, swagger_1.ApiOperation)({ summary: 'Update client booking settings for a clinic' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Client booking settings updated successfully.',
        type: clinic_entity_1.ClinicEntity // Return the updated clinic entity
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Clinic not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Validation failed' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('updateClientBookingSettings-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_client_booking_settings_dto_1.UpdateClientBookingSettingsDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "updateClientBookingSettings", null);
__decorate([
    (0, common_1.Get)(':id/settings'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.LAB_TECHNICIAN),
    (0, swagger_1.ApiOperation)({ summary: 'Get clinic settings' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the clinic settings.',
        type: clinic_settings_dto_1.ClinicSettingsResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Clinic not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('getClinicSettings-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "getClinicSettings", null);
__decorate([
    (0, common_1.Put)(':id/settings'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.LAB_TECHNICIAN),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, skipMissingProperties: true })),
    (0, swagger_1.ApiOperation)({ summary: 'Update clinic settings' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Clinic settings updated successfully.',
        type: clinic_settings_dto_1.ClinicSettingsResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Clinic not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Validation failed' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('updateClinicSettings-clinics'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, clinic_settings_dto_1.ClinicSettingsDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicController.prototype, "updateClinicSettings", null);
exports.ClinicController = ClinicController = __decorate([
    (0, swagger_1.ApiTags)('Clinic'),
    (0, common_1.Controller)('clinics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        clinic_service_1.ClinicService])
], ClinicController);
//# sourceMappingURL=clinic.controller.js.map