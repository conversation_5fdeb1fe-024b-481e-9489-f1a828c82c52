"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentDetailsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentDetailsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const payment_details_entity_1 = require("./entities/payment-details.entity");
const typeorm_2 = require("typeorm");
const patients_service_1 = require("../patients/patients.service");
const receipt_1 = require("../utils/pdfs/receipt");
const generatePdf_1 = require("../utils/generatePdf");
const enum_credit_types_1 = require("./enums/enum-credit-types");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const send_mail_service_1 = require("../utils/aws/ses/send-mail-service");
const uuidv7_1 = require("uuidv7");
const generate_alpha_numeric_code_1 = require("../utils/common/generate_alpha-numeric_code");
const whatsapp_service_1 = require("../utils/whatsapp-integration/whatsapp.service");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const patient_owner_entity_1 = require("../patients/entities/patient-owner.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const global_owner_entity_1 = require("../owners/entities/global-owner.entity");
const enum_invoice_status_1 = require("../invoice/enums/enum-invoice-status");
const credit_transaction_entity_1 = require("../credits/entities/credit-transaction.entity");
const enum_credit_transaction_type_1 = require("../credits/enums/enum-credit-transaction-type");
const enum_payment_types_1 = require("../invoice/enums/enum-payment-types");
const enum_invoice_types_1 = require("../invoice/enums/enum-invoice-types");
const sqs_service_1 = require("../utils/aws/sqs/sqs.service");
const enum_derived_credit_transaction_type_1 = require("../credits/enums/enum-derived-credit-transaction-type");
const invoice_audit_log_entity_1 = require("../invoice/entities/invoice-audit-log.entity");
const payment_details_audit_log_entity_1 = require("./entities/payment-details-audit-log.entity");
let PaymentDetailsService = PaymentDetailsService_1 = class PaymentDetailsService {
    constructor(paymentDetailsRepository, paymentDetailsAuditLogRepository, patientService, s3Service, mailService, whatsappService, loggerService, clinicRepository, patientOwnerRepository, invoiceRepository, invoiceAuditLogRepository, ownerBrandRepository, creditTransactionRepository, connection, sqsService) {
        this.paymentDetailsRepository = paymentDetailsRepository;
        this.paymentDetailsAuditLogRepository = paymentDetailsAuditLogRepository;
        this.patientService = patientService;
        this.s3Service = s3Service;
        this.mailService = mailService;
        this.whatsappService = whatsappService;
        this.loggerService = loggerService;
        this.clinicRepository = clinicRepository;
        this.patientOwnerRepository = patientOwnerRepository;
        this.invoiceRepository = invoiceRepository;
        this.invoiceAuditLogRepository = invoiceAuditLogRepository;
        this.ownerBrandRepository = ownerBrandRepository;
        this.creditTransactionRepository = creditTransactionRepository;
        this.connection = connection;
        this.sqsService = sqsService;
        this.logger = new common_1.Logger(PaymentDetailsService_1.name);
    }
    async createPaymentDetails(paymentDetailsDto, clinicId, brandId, userId) {
        const queryRunner = this.connection.createQueryRunner();
        const logContext = {
            clinicId,
            brandId,
            userId,
            ownerId: paymentDetailsDto.ownerId,
            invoiceId: paymentDetailsDto.invoiceId,
            patientId: paymentDetailsDto.patientId,
            requestId: (0, uuidv7_1.uuidv4)()
        };
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.logPaymentEvent(this.logger, logContext, 'Start creating payment detail', { paymentDetailsDto });
            // Fetch owner data
            const ownerData = await this.fetchOwnerData(queryRunner, paymentDetailsDto.ownerId, brandId);
            // Validate invoice
            const invoice = paymentDetailsDto.invoiceId
                ? await this.validateInvoice(queryRunner, paymentDetailsDto.invoiceId, paymentDetailsDto)
                : null;
            // Calculate credits and excess
            const { newCredits, amountToApply, creditAmountUsed, excessAmount } = this.calculateNewBalances(paymentDetailsDto, ownerData, invoice);
            const createdPayments = [];
            // Generate separate reference IDs for different payment types
            const cashReferenceAlphaId = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('referenceAlphaId', this.paymentDetailsRepository);
            // For credit notes, we may need separate reference IDs
            const isCreditNoteWithExcess = paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.CreditNote &&
                excessAmount > 0;
            // Create cash payment entity if amount > 0 or it's a special case
            if ((Number(paymentDetailsDto.amount) > 0 ||
                // Allow zero-amount payments for invoice types with transaction amount
                (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Invoice &&
                    Number(paymentDetailsDto.transactionAmount) > 0) ||
                // Allow zero-amount payments for Credit Notes
                paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.CreditNote) &&
                // We want to create a cash payment in all cases EXCEPT when:
                // 1. It's a credit-to-cash conversion (Return type with isCreditUsed=true)
                // 2. The amount to apply is zero and it's not an Invoice payment or Credit Note
                (amountToApply > 0 ||
                    paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Invoice ||
                    paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.CreditNote ||
                    // Special case for credit-to-cash conversion
                    (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Return &&
                        paymentDetailsDto.isCreditUsed))) {
                const cashPayment = await this.createCashPaymentEntity(queryRunner, paymentDetailsDto, paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Return &&
                    paymentDetailsDto.isCreditUsed
                    ? paymentDetailsDto.amount // For credit-to-cash, use full amount
                    : amountToApply, // For other cases, use amountToApply
                cashReferenceAlphaId, clinicId, brandId, userId);
                createdPayments.push(cashPayment);
                // Create credit transaction for Collect payment
                if (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Collect) {
                    await this.createCreditTransaction(queryRunner, ownerData.id, paymentDetailsDto.amount, enum_credit_transaction_type_1.CreditTransactionType.ADD, `Amount ${paymentDetailsDto.amount} added to credits via Collect payment`, {
                        paymentId: cashPayment.id,
                        invoiceId: null
                    }, clinicId, brandId, userId);
                }
            }
            // Create credit payment entity if credits are used
            if (paymentDetailsDto.isCreditUsed &&
                creditAmountUsed > 0 &&
                paymentDetailsDto.type !== enum_credit_types_1.EnumAmountType.Return // Don't create credit payment for credit-to-cash conversion
            ) {
                const creditReferenceAlphaId = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('referenceAlphaId', this.paymentDetailsRepository);
                const creditPayment = await this.createCreditPaymentEntity(queryRunner, paymentDetailsDto, creditAmountUsed, creditReferenceAlphaId, clinicId, brandId, userId);
                createdPayments.push(creditPayment);
                // Create credit transaction for credit usage
                await this.createCreditTransaction(queryRunner, ownerData.id, creditAmountUsed, enum_credit_transaction_type_1.CreditTransactionType.USE, `Credits used for ${paymentDetailsDto.invoiceId ? 'invoice ' + paymentDetailsDto.invoiceId : 'payment'}`, {
                    paymentId: creditPayment.id,
                    invoiceId: paymentDetailsDto.invoiceId
                }, clinicId, brandId, userId);
            }
            // For credit-to-cash conversion, create a credit transaction to track credit usage
            if (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Return &&
                paymentDetailsDto.isCreditUsed &&
                creditAmountUsed > 0) {
                await this.createCreditTransaction(queryRunner, ownerData.id, creditAmountUsed, enum_credit_transaction_type_1.CreditTransactionType.USE, `Credits converted to cash return`, {
                    paymentId: createdPayments[0].id, // Reference the cash payment
                    invoiceId: null
                }, clinicId, brandId, userId);
            }
            // Update invoice status if applicable
            if (invoice) {
                await this.updateInvoiceStatus(queryRunner, invoice, paymentDetailsDto, amountToApply, creditAmountUsed);
            }
            // Handle excess amount from credit notes specially
            if (excessAmount > 0 &&
                paymentDetailsDto.type !== enum_credit_types_1.EnumAmountType.Collect) {
                // Generate a new reference ID for excess payment in credit notes
                const excessReferenceAlphaId = isCreditNoteWithExcess
                    ? await (0, generate_alpha_numeric_code_1.generateUniqueCode)('referenceAlphaId', this.paymentDetailsRepository)
                    : cashReferenceAlphaId;
                // Create the excess payment
                const excessPayment = await this.handleExcessAmountAsCredits(queryRunner, paymentDetailsDto, excessAmount, excessReferenceAlphaId, clinicId, brandId, userId, logContext, 
                // Only for credit notes, include invoice ID and patient ID
                isCreditNoteWithExcess
                    ? {
                        includeInvoiceId: true,
                        includePatientId: true
                    }
                    : undefined);
                createdPayments.push(excessPayment);
            }
            // Update owner credits
            ownerData.ownerCredits = newCredits;
            await queryRunner.manager.save(ownerData);
            await queryRunner.commitTransaction();
            this.logPaymentEvent(this.logger, logContext, 'Payment processing completed', { paymentIds: createdPayments.map(p => p.id), newCredits });
            return createdPayments;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logPaymentEvent(this.logger, logContext, 'Error in creating payment', { errorMessage: error.message }, 'error');
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    // New method for creating cash payment entity
    async createCashPaymentEntity(queryRunner, paymentDetailsDto, cashAmount, referenceAlphaId, clinicId, brandId, userId) {
        const payment = new payment_details_entity_1.PaymentDetailsEntity();
        payment.ownerId = paymentDetailsDto.ownerId;
        payment.invoiceId = paymentDetailsDto.invoiceId;
        if (paymentDetailsDto.patientId) {
            payment.patientId = paymentDetailsDto.patientId;
        }
        payment.type = paymentDetailsDto.type;
        payment.amount = cashAmount;
        payment.paymentType = paymentDetailsDto.paymentType || 'Cash';
        payment.transactionAmount = paymentDetailsDto.transactionAmount || 0;
        payment.amountPayable = paymentDetailsDto.amountPayable || cashAmount;
        // Handle credit usage for different payment types
        if (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Return &&
            paymentDetailsDto.isCreditUsed) {
            // For refunds with credit use, set isCreditUsed=true and creditAmountUsed
            payment.isCreditUsed = true;
            payment.creditAmountUsed =
                paymentDetailsDto.creditAmountUsed || cashAmount;
        }
        else {
            payment.isCreditUsed = false;
            payment.creditAmountUsed = 0;
        }
        // Set credits added flag and amount correctly for Collect type payments
        if (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Collect) {
            payment.isCreditsAdded = true;
            payment.creditAmountAdded = cashAmount;
        }
        else {
            payment.isCreditsAdded = false;
            payment.creditAmountAdded = 0;
        }
        payment.paymentNotes = paymentDetailsDto.paymentNotes;
        payment.showInInvoice = true;
        payment.showInLedger = true;
        payment.brandId = brandId;
        payment.clinicId = clinicId;
        payment.previousBalance = 0;
        payment.mainBalance = 0;
        payment.createdBy = userId;
        payment.updatedBy = userId;
        payment.referenceAlphaId = referenceAlphaId;
        payment.receiptDetail = {};
        return await queryRunner.manager.save(payment);
    }
    // New method for creating credit payment entity
    async createCreditPaymentEntity(queryRunner, paymentDetailsDto, creditAmount, referenceAlphaId, clinicId, brandId, userId) {
        const payment = new payment_details_entity_1.PaymentDetailsEntity();
        payment.ownerId = paymentDetailsDto.ownerId;
        payment.invoiceId = paymentDetailsDto.invoiceId;
        if (paymentDetailsDto.patientId) {
            payment.patientId = paymentDetailsDto.patientId;
        }
        payment.type = paymentDetailsDto.invoiceId
            ? enum_credit_types_1.EnumAmountType.ReconcileInvoice
            : paymentDetailsDto.type;
        payment.amount = 0; // Cash amount is 0 for credit payment
        payment.paymentType = enum_payment_types_1.EnumPaymentType.Credits;
        payment.transactionAmount = 0;
        payment.amountPayable = 0;
        payment.isCreditUsed = true;
        payment.creditAmountUsed = creditAmount;
        payment.isCreditsAdded = false;
        payment.creditAmountAdded = 0;
        payment.paymentNotes = paymentDetailsDto.paymentNotes;
        payment.showInInvoice = true;
        payment.showInLedger = true;
        payment.brandId = brandId;
        payment.clinicId = clinicId;
        payment.previousBalance = 0;
        payment.mainBalance = 0;
        payment.createdBy = userId;
        payment.updatedBy = userId;
        payment.referenceAlphaId = referenceAlphaId;
        payment.receiptDetail = {};
        return await queryRunner.manager.save(payment);
    }
    async handleExcessAmountAsCredits(queryRunner, paymentDetailsDto, excessAmount, referenceAlphaId, clinicId, brandId, userId, logContext, options) {
        const excessPayment = new payment_details_entity_1.PaymentDetailsEntity();
        excessPayment.ownerId = paymentDetailsDto.ownerId;
        excessPayment.type = enum_credit_types_1.EnumAmountType.Collect;
        excessPayment.amount = excessAmount;
        excessPayment.paymentType = (options === null || options === void 0 ? void 0 : options.includeInvoiceId)
            ? enum_payment_types_1.EnumPaymentType.Credits
            : paymentDetailsDto.paymentType || 'Cash';
        excessPayment.transactionAmount = 0;
        excessPayment.amountPayable = 0;
        excessPayment.isCreditUsed = false;
        excessPayment.creditAmountUsed = 0;
        excessPayment.isCreditsAdded = true;
        excessPayment.creditAmountAdded = excessAmount;
        excessPayment.paymentNotes = paymentDetailsDto.description;
        excessPayment.showInInvoice = true;
        excessPayment.showInLedger = true;
        excessPayment.brandId = brandId;
        excessPayment.clinicId = clinicId;
        // Include invoice ID and patient ID if requested (for credit notes)
        if ((options === null || options === void 0 ? void 0 : options.includeInvoiceId) && paymentDetailsDto.invoiceId) {
            excessPayment.invoiceId = paymentDetailsDto.invoiceId;
        }
        if ((options === null || options === void 0 ? void 0 : options.includePatientId) && paymentDetailsDto.patientId) {
            excessPayment.patientId = paymentDetailsDto.patientId;
        }
        excessPayment.previousBalance = 0; // No balance tracking
        excessPayment.mainBalance = 0; // No balance tracking
        excessPayment.createdBy = userId;
        excessPayment.updatedBy = userId;
        excessPayment.referenceAlphaId = referenceAlphaId;
        excessPayment.receiptDetail = {};
        const savedExcessPayment = await queryRunner.manager.save(excessPayment);
        await this.createCreditTransaction(queryRunner, paymentDetailsDto.ownerId, excessAmount, enum_credit_transaction_type_1.CreditTransactionType.ADD, `Excess payment of ${excessAmount} added to credits`, {
            paymentId: savedExcessPayment.id,
            invoiceId: excessPayment.invoiceId // Use the assigned invoice ID if available
        }, clinicId, brandId, userId);
        this.logPaymentEvent(this.logger, logContext, 'Handled excess amount as credits', { excessAmount, paymentId: savedExcessPayment.id });
        return savedExcessPayment;
    }
    // Helper Function Implementations
    async fetchOwnerData(queryRunner, ownerId, brandId) {
        const ownerData = await queryRunner.manager.findOne(owner_brand_entity_1.OwnerBrand, {
            where: { id: ownerId, brandId },
            lock: { mode: 'pessimistic_write' }
        });
        if (!ownerData)
            throw new Error('Owner not found');
        if (ownerData.globalOwnerId) {
            const globalOwnerData = await queryRunner.manager.findOne(global_owner_entity_1.GlobalOwner, {
                where: { id: ownerData.globalOwnerId }
            });
            ownerData.globalOwner = globalOwnerData || null;
        }
        return ownerData;
    }
    async validateInvoice(queryRunner, invoiceId, paymentDetailsDto) {
        const invoice = await queryRunner.manager.findOne(invoice_entity_1.InvoiceEntity, {
            where: { id: invoiceId }
        });
        if (invoice && !invoice.details) {
            paymentDetailsDto.type = enum_credit_types_1.EnumAmountType.Collect;
        }
        return invoice || null;
    }
    calculateNewBalances(paymentDetailsDto, ownerData, invoice) {
        const amount = Number(paymentDetailsDto.amount);
        const transactionAmount = Number(paymentDetailsDto.transactionAmount || 0);
        let newCredits = Number(ownerData.ownerCredits || 0);
        let creditAmountUsed = 0;
        let amountToApply = amount;
        let excessAmount = 0;
        switch (paymentDetailsDto.type) {
            case enum_credit_types_1.EnumAmountType.Collect:
                // For Collect payments, entire amount goes to owner credits
                // isCreditsAdded and creditAmountAdded are set in createCashPaymentEntity
                // Credit transaction is created after creating the cash payment
                newCredits += amount;
                excessAmount = amount; // Entire amount goes to credits
                paymentDetailsDto.isCreditAdded = true;
                paymentDetailsDto.creditAmountAdded = amount;
                break;
            case enum_credit_types_1.EnumAmountType.Return:
                // Check if this is a credit-to-cash conversion
                if (paymentDetailsDto.isCreditUsed && amount > 0) {
                    creditAmountUsed = amount; // Amount of credits to convert to cash
                    if (newCredits < creditAmountUsed) {
                        throw new Error('Insufficient credits for return');
                    }
                    newCredits -= creditAmountUsed;
                    paymentDetailsDto.isCreditUsed = true;
                    paymentDetailsDto.creditAmountUsed = creditAmountUsed;
                    // For credit-to-cash conversion, we don't need a separate cash payment
                    amountToApply = 0;
                }
                else {
                    // Regular cash return
                    amountToApply = amount;
                    creditAmountUsed = 0;
                    paymentDetailsDto.isCreditUsed = false;
                    paymentDetailsDto.creditAmountUsed = 0;
                }
                break;
            case enum_credit_types_1.EnumAmountType.Invoice:
            case enum_credit_types_1.EnumAmountType.ReconcileInvoice:
                if (invoice) {
                    const pendingAmount = Number(invoice.invoiceAmount) -
                        Number(invoice.paidAmount || 0);
                    // Check if cash amount exceeds pending amount
                    if (amount > pendingAmount) {
                        excessAmount = amount - pendingAmount;
                        amountToApply = pendingAmount;
                        paymentDetailsDto.amount = amountToApply;
                        newCredits += excessAmount;
                        // No credit needed since cash alone covers the invoice
                        creditAmountUsed = 0;
                    }
                    else {
                        // Cash amount is less than or equal to pending amount
                        amountToApply = amount;
                        // If credits are to be used, calculate credit amount needed
                        if (paymentDetailsDto.isCreditUsed) {
                            const remainingAfterCash = pendingAmount - amount;
                            const requestedCreditAmount = Number(paymentDetailsDto.creditAmountUsed || 0);
                            // Use the minimum of: requested credit, available credit, remaining amount needed
                            creditAmountUsed = Math.min(requestedCreditAmount, newCredits, remainingAfterCash);
                            newCredits -= creditAmountUsed;
                            // Check if there's still excess after using both cash and credit
                            const totalPayment = amount + creditAmountUsed;
                            if (totalPayment > pendingAmount) {
                                // This shouldn't happen with the logic above, but safety check
                                const excess = totalPayment - pendingAmount;
                                excessAmount = excess;
                                newCredits += excess;
                                creditAmountUsed -= excess; // Adjust credit used
                            }
                        }
                        else {
                            creditAmountUsed = 0;
                        }
                    }
                }
                break;
            case enum_credit_types_1.EnumAmountType.CreditNote:
                if (transactionAmount > amount) {
                    excessAmount = transactionAmount - amount;
                    newCredits += excessAmount;
                }
                else if (amount > transactionAmount) {
                    excessAmount = amount - transactionAmount;
                    newCredits += excessAmount;
                }
                break;
        }
        return {
            newCredits,
            amountToApply,
            creditAmountUsed,
            excessAmount
        };
    }
    async updateInvoiceStatus(queryRunner, invoice, paymentDetailsDto, amountToApply, creditAmountUsed) {
        // Calculate total payment amount including cash and credits
        const totalPayment = Number(amountToApply) + Number(creditAmountUsed);
        const newPaidAmount = Number(invoice.paidAmount || 0) + totalPayment;
        const newBalanceDue = Math.max(0, Number(invoice.invoiceAmount) - newPaidAmount);
        // Determine status based on balance
        const newStatus = newBalanceDue <= 0.01 // Allow for small rounding differences
            ? enum_invoice_status_1.EnumInvoiceStatus.FULLY_PAID
            : newPaidAmount > 0
                ? enum_invoice_status_1.EnumInvoiceStatus.PARTIALLY_PAID
                : invoice.status;
        await queryRunner.manager.update(invoice_entity_1.InvoiceEntity, { id: invoice.id }, {
            status: newStatus,
            paidAmount: newPaidAmount,
            balanceDue: newBalanceDue,
            updatedAt: new Date()
        });
        if (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.CreditNote &&
            invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Refund) {
            await queryRunner.manager.update(invoice_entity_1.InvoiceEntity, { id: invoice.id }, {
                status: enum_invoice_status_1.EnumInvoiceStatus.FULLY_PAID,
                balanceDue: 0,
                updatedAt: new Date()
            });
        }
    }
    async createCreditTransaction(queryRunner, ownerId, amount, transactionType, description, relatedIds, clinicId, brandId, userId) {
        let derivedTransactionType = enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.UNKNOWN;
        let paymentDetail = null;
        if (relatedIds.paymentId) {
            // Fetch the paymentDetail within the same transaction
            paymentDetail = await queryRunner.manager.findOne(payment_details_entity_1.PaymentDetailsEntity, {
                where: { id: relatedIds.paymentId }
            });
        }
        // --- Logic to determine derived_transaction_type based on migration rules ---
        if (transactionType === enum_credit_transaction_type_1.CreditTransactionType.USE) {
            if (paymentDetail) {
                // Rule 1: CREDITS_RETURNED
                if (paymentDetail.type === enum_credit_types_1.EnumAmountType.Return) {
                    derivedTransactionType =
                        enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_RETURNED;
                }
                // Rule 2: CREDITS_USED (with specific payment types)
                else if (paymentDetail.type === enum_credit_types_1.EnumAmountType.ReconcileInvoice ||
                    paymentDetail.type === enum_credit_types_1.EnumAmountType.BulkReconcileInvoice) {
                    derivedTransactionType =
                        enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_USED;
                }
                // If paymentDetail exists but type doesn't match above for USE, it might remain UNKNOWN
                // or you might decide a default. Migration default for USE is CREDITS_USED if no specific conditions met.
                // However, migration rule 7 (USE without payment_detail_id) is handled below.
                // If paymentDetail IS present, and type is not Return/Reconcile/BulkReconcile, it will be UNKNOWN by default here.
                // This seems fine as migration does not specify other types for USE.
            }
            else {
                // Rule 7: Default case for USE transactions without payment details
                derivedTransactionType =
                    enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_USED;
            }
        }
        else if (transactionType === enum_credit_transaction_type_1.CreditTransactionType.ADD) {
            if (paymentDetail) {
                // Rule 3: EXCESS_PAYMENT
                if (paymentDetail.type === enum_credit_types_1.EnumAmountType.Collect) {
                    if (paymentDetail.amountPayable === 0 &&
                        (paymentDetail.creditAmountAdded > 0 || amount > 0) // `amount` is ct.amount
                    ) {
                        derivedTransactionType =
                            enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.EXCESS_PAYMENT;
                    }
                    // Rule 4a: CREDITS_ADDED (Direct Purchase/Grant)
                    else if (paymentDetail.amountPayable > 0) {
                        derivedTransactionType =
                            enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_ADDED;
                    }
                }
                // Rule 5: CREDITS_ADDED (Refund to Credits)
                else if (paymentDetail.type === enum_credit_types_1.EnumAmountType.Return) {
                    derivedTransactionType =
                        enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_ADDED;
                }
                // If paymentDetail exists but type doesn't match above for ADD, it might remain UNKNOWN.
                // Migration rule 8 (ADD without payment_detail_id) is handled below.
            }
            else {
                // Rule 6: Special case "Credit added from previous positive balance"
                if (description ===
                    'Credit added from previous positive balance') {
                    derivedTransactionType =
                        enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_ADDED;
                }
                // Rule 8: Default case for ADD transactions without payment details (and not matching special description)
                else {
                    derivedTransactionType =
                        enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_ADDED;
                }
            }
        }
        // Fetch the owner to update credit balance
        const owner = await queryRunner.manager.findOne(owner_brand_entity_1.OwnerBrand, {
            where: { id: ownerId }
        });
        if (!owner) {
            this.loggerService.error('Owner not found for credit transaction', {
                ownerId,
                transactionType,
                amount
            });
            throw new Error(`Owner with ID ${ownerId} not found`);
        }
        this.loggerService.log('🏦 Current owner credit balance before transaction', {
            ownerId,
            currentCredits: owner.ownerCredits,
            transactionType,
            transactionAmount: amount
        });
        // Update owner credit balance based on transaction type
        let newCreditBalance;
        if (transactionType === enum_credit_transaction_type_1.CreditTransactionType.ADD) {
            // Add credits to owner balance
            newCreditBalance = Number(owner.ownerCredits || 0) + Number(amount);
            this.loggerService.log('💰 Adding credits to owner balance', {
                ownerId,
                previousBalance: owner.ownerCredits,
                creditAmountToAdd: amount,
                newBalance: newCreditBalance
            });
        }
        else if (transactionType === enum_credit_transaction_type_1.CreditTransactionType.USE) {
            // Deduct credits from owner balance
            newCreditBalance = Math.max(0, Number(owner.ownerCredits || 0) - Number(amount));
            this.loggerService.log('💳 Deducting credits from owner balance', {
                ownerId,
                previousBalance: owner.ownerCredits,
                creditAmountToDeduct: amount,
                newBalance: newCreditBalance,
                preventedNegative: newCreditBalance !== (Number(owner.ownerCredits || 0) - Number(amount))
            });
        }
        else {
            // For other transaction types, keep balance unchanged
            newCreditBalance = Number(owner.ownerCredits || 0);
            this.loggerService.log('⚠️ Unknown transaction type, keeping balance unchanged', {
                ownerId,
                transactionType,
                currentBalance: newCreditBalance
            });
        }
        // Update owner credit balance
        owner.ownerCredits = newCreditBalance;
        await queryRunner.manager.save(owner);
        this.loggerService.log('✅ Owner credit balance updated successfully', {
            ownerId,
            transactionType,
            transactionAmount: amount,
            finalBalance: newCreditBalance
        });
        const creditTransaction = queryRunner.manager.create(credit_transaction_entity_1.CreditTransactionEntity, {
            ownerId,
            amount,
            transactionType,
            derivedTransactionType,
            description,
            invoiceId: relatedIds.invoiceId,
            paymentDetailId: relatedIds.paymentId,
            clinicId,
            brandId,
            createdBy: userId,
            updatedBy: userId
        });
        const savedTransaction = await queryRunner.manager.save(creditTransaction);
        this.loggerService.log('📝 Credit transaction record created', {
            transactionId: savedTransaction.id,
            ownerId,
            amount,
            transactionType,
            derivedTransactionType,
            description
        });
    }
    async verifyCreditAvailability(queryRunner, bulkPaymentDto, ownerData, logContext) {
        const availableOwnerCredits = Number(ownerData.ownerCredits || 0);
        let creditAmountToUse = 0;
        if (bulkPaymentDto.isCreditUsed &&
            bulkPaymentDto.creditAmountUsed &&
            bulkPaymentDto.creditAmountUsed > 0) {
            if (availableOwnerCredits < bulkPaymentDto.creditAmountUsed) {
                this.logPaymentEvent(this.logger, logContext, 'Insufficient credits for bulk payment', {
                    availableCredits: availableOwnerCredits,
                    requestedCredits: bulkPaymentDto.creditAmountUsed
                }, 'error');
                throw new Error('Insufficient credits available');
            }
            creditAmountToUse = bulkPaymentDto.creditAmountUsed;
        }
        return { creditAmountToUse };
    }
    logPaymentEvent(logger, logContext, message, additionalData, level = 'log') {
        logger[level](message, { ...logContext, ...additionalData });
    }
    /**
     * Get payment details for a patient's owner, including soft deleted, cancelled, and written off invoices
     * @param patientId - The ID of the patient
     * @returns Payment details with total count and owner details
     */
    async getPaymentDetailsForPatientsOwner(patientId) {
        // Step 1: Fetch the primary owner
        const primaryOwner = await this.getPrimaryOwner(patientId);
        if (!primaryOwner) {
            return { paymentDetails: [], total: 0, ownerDetails: null };
        }
        // Step 2: Build and execute the payment details query
        const paymentDetailsQuery = this.paymentDetailsRepository
            .createQueryBuilder('payment')
            .leftJoinAndSelect('payment.invoice', 'invoice')
            .leftJoinAndSelect('payment.patient', 'patient')
            .leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
            .leftJoinAndMapOne('payment.creator', 'users', 'user', 'payment.created_by = user.id')
            // Exclude soft deleted payment details
            .where('payment.deleted_at IS NULL')
            .andWhere(
        // For Invoice and Credit Note - show all for the owner and patient
        '(payment.type IN (:...invoiceTypes) AND payment.patient_id = :patientId) OR ' +
            // For Bulk Reconcile Invoice - include all for this owner regardless of patient
            '(payment.type = :bulkReconcileType AND payment.owner_id = :ownerId) OR ' +
            // For other payment types - require both owner and patient match
            '(payment.type NOT IN (:...invoiceTypes) AND payment.type != :bulkReconcileType AND payment.owner_id = :ownerId AND payment.patient_id = :patientId)', {
            ownerId: primaryOwner.ownerBrand.id,
            patientId,
            invoiceTypes: ['Invoice', 'Credit Note'],
            bulkReconcileType: 'Bulk Reconcile Invoice'
        });
        // Add query for related payments
        paymentDetailsQuery.orWhere('payment.reference_alpha_id IN ' +
            '(SELECT DISTINCT p.reference_alpha_id FROM payment_details p WHERE p.patient_id IS NULL AND p.owner_id = :ownerId2)', { ownerId2: primaryOwner.ownerBrand.id });
        paymentDetailsQuery.orderBy('payment.created_at', 'DESC');
        const [paymentDetailsResults] = await paymentDetailsQuery.getManyAndCount();
        // Step 3: Map results and add createdByName
        const paymentDetails = paymentDetailsResults.map(payment => {
            const result = payment;
            const creator = payment.creator;
            result.createdByName =
                creator && (creator.firstName || creator.lastName)
                    ? `${creator.firstName || ''} ${creator.lastName || ''}`.trim()
                    : result.createdBy
                        ? 'Staff Member'
                        : 'System';
            return result;
        });
        // Step 4: Filter out invalid invoices
        const initialFilteredPaymentDetails = paymentDetails.filter(pd => {
            var _a, _b, _c;
            return pd.type === 'Invoice'
                ? (((_c = (_b = (_a = pd.invoice) === null || _a === void 0 ? void 0 : _a.details) === null || _b === void 0 ? void 0 : _b.filter(item => item.isAddedToCart === true)) === null || _c === void 0 ? void 0 : _c.length) || 0) > 0
                : true;
        });
        // Step 5: Build refunded items map for credit notes
        const refundedItemsMap = this.buildRefundedItemsMap(initialFilteredPaymentDetails);
        // Step 6: Consolidate payments with same referenceAlphaId
        const consolidatedPayments = this.consolidatePayments(initialFilteredPaymentDetails);
        // Step 7: Fetch and group related payments for all invoices in one query
        const invoiceIds = consolidatedPayments
            .filter(pd => (pd.type === 'Invoice' || pd.type === 'Credit Note') &&
            pd.invoiceId)
            .map(pd => pd.invoiceId);
        const relatedPaymentsMap = await this.fetchRelatedPayments(invoiceIds);
        // Step 8: Enhance payment details with related payments and refund info
        const enhancedPaymentDetails = consolidatedPayments.map(pd => {
            var _a, _b;
            const enhancedPd = pd;
            // For Invoice and Credit Note types, attach payments by invoiceId
            if ((enhancedPd.type === 'Invoice' ||
                enhancedPd.type === 'Credit Note') &&
                enhancedPd.invoiceId) {
                const relatedPayments = relatedPaymentsMap.get(enhancedPd.invoiceId) || [];
                enhancedPd.payments =
                    this.processRelatedPayments(relatedPayments);
                if (((_a = enhancedPd.invoice) === null || _a === void 0 ? void 0 : _a.cartId) && ((_b = enhancedPd.invoice) === null || _b === void 0 ? void 0 : _b.details)) {
                    enhancedPd.invoice.details =
                        this.addRefundInfoToInvoiceDetails(enhancedPd.invoice.details, refundedItemsMap.get(enhancedPd.invoice.cartId) ||
                            new Map());
                }
            }
            else {
                enhancedPd.payments = [];
            }
            // Only transform "Collect" type payments to "Reconcile Invoice" when not processed by consolidation
            if (enhancedPd.type === 'Collect' &&
                enhancedPd.relatedPayments &&
                enhancedPd.hasCollectPayment &&
                enhancedPd.relatedPayments.length > 1) {
                enhancedPd.type = enum_credit_types_1.EnumAmountType.ReconcileInvoice;
            }
            return enhancedPd;
        });
        // Step 9: Add 'Reconcile Invoice' duplicates
        const finalPaymentDetails = this.addReconcileInvoices(enhancedPaymentDetails);
        // Step 10: Filter payment details based on patient and owner
        const patientFilteredPaymentDetails = finalPaymentDetails.filter(paymentDetail => {
            // For Invoice and Credit Note types, check both patient and owner
            if (paymentDetail.type === 'Invoice' ||
                paymentDetail.type === 'Credit Note') {
                return paymentDetail.patientId === patientId;
            }
            // Special case for Bulk Reconcile Invoice - include all entries for this owner
            if (paymentDetail.type === 'Bulk Reconcile Invoice') {
                return paymentDetail.ownerId === primaryOwner.ownerBrand.id;
            }
            // For other types (Collect, Return, ReconcileInvoice) filter by both patientId and ownerId
            return paymentDetail.ownerId === primaryOwner.ownerBrand.id;
        });
        // Step 11: Return the result
        return {
            paymentDetails: patientFilteredPaymentDetails,
            total: patientFilteredPaymentDetails.length,
            ownerDetails: primaryOwner.ownerBrand
                ? {
                    createdAt: primaryOwner.ownerBrand.createdAt,
                    openingBalance: primaryOwner.ownerBrand.openingBalance || 0
                }
                : null
        };
    }
    /**
     * Get payment details for a specific patient with optional search on reference_alpha_id
     * @param patientId - The ID of the patient
     * @param search - Optional search term for reference_alpha_id
     * @param page - Page number for pagination
     * @param limit - Number of items per page
     * @returns Payment details with pagination info
     */
    async getPaymentDetailsForPatient(patientId, search, page = 1, limit = 20) {
        try {
            // Step 1: Get the primary owner of the patient to filter related payments
            const primaryOwner = await this.getPrimaryOwner(patientId);
            if (!primaryOwner) {
                return {
                    paymentDetails: [],
                    total: 0,
                    ownerDetails: null,
                    totalPages: 0,
                    currentPage: page,
                    uniqueUsers: []
                };
            }
            // Step 2: Build comprehensive query with complex business logic
            const queryParams = {
                patientId,
                ownerId: primaryOwner.ownerBrand.id
            };
            // Build the base query with advanced joins excluding soft deleted records
            let query = this.paymentDetailsRepository
                .createQueryBuilder('payment')
                .leftJoinAndSelect('payment.invoice', 'invoice')
                .leftJoin('payment.patient', 'patient')
                .leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
                .leftJoinAndMapOne('payment.creator', 'users', 'user', 'payment.created_by = user.id')
                // Exclude soft deleted payment details
                .where('payment.deleted_at IS NULL')
                .andWhere(
            // For Invoice and Credit Note - show all for the patient
            '(payment.type IN (:...invoiceTypes) AND payment.patient_id = :patientId) OR ' +
                // For Bulk Reconcile Invoice - include only if patientId matches
                '(payment.type = :bulkReconcileType AND payment.owner_id = :ownerId AND payment.patient_id = :patientId) OR ' +
                // For other payment types - require both owner and patient match
                '(payment.type NOT IN (:...invoiceTypes) AND payment.type != :bulkReconcileType AND payment.owner_id = :ownerId AND payment.patient_id = :patientId) OR ' +
                // Include entries with null patientId that belong to this owner
                '(payment.patient_id IS NULL AND payment.owner_id = :ownerId)', {
                ...queryParams,
                invoiceTypes: ['Invoice', 'Credit Note'],
                bulkReconcileType: 'Bulk Reconcile Invoice'
            });
            // Add query for related payments (bulk reconcile entries without patient ID)
            query.orWhere('payment.reference_alpha_id IN ' +
                '(SELECT DISTINCT p.reference_alpha_id FROM payment_details p WHERE p.patient_id = :patientId2 AND p.owner_id = :ownerId2)', { patientId2: patientId, ownerId2: primaryOwner.ownerBrand.id });
            // Add search filter for reference_alpha_id if provided
            if (search && search.trim() !== '') {
                query = query.andWhere('payment.referenceAlphaId ILIKE :search', {
                    search: `%${search.trim()}%`
                });
            }
            // Filter out empty invoices - critical for financial accuracy
            query = query.andWhere(`(
				payment.type != 'Invoice' OR
				(invoice.details IS NOT NULL AND jsonb_array_length(
					jsonb_path_query_array(invoice.details, '$[*] ? (@.isAddedToCart == true)')
				) > 0)
			)`);
            // Filter out entries with no financial impact - essential for reporting
            query = query.andWhere(`(
				payment.amount > 0 OR
				payment.credit_amount_used > 0 OR
				payment.credit_amount_added > 0
			)`);
            // Apply ordering
            query = query.orderBy('payment.created_at', 'DESC');
            // Execute the query to get ALL matching results for comprehensive processing
            const allPaymentDetailsResults = await query.getMany();
            // Step 3: Fetch unique users who created payment details for comprehensive reporting
            const uniqueUsers = [];
            // Map results and add createdByName
            const allPaymentDetailsMapped = allPaymentDetailsResults.map(payment => {
                const result = payment;
                const creator = payment.creator;
                result.createdByName =
                    creator && (creator.firstName || creator.lastName)
                        ? `${creator.firstName || ''} ${creator.lastName || ''}`.trim()
                        : result.createdBy
                            ? 'Staff Member'
                            : 'System';
                return result;
            });
            // Consolidate payments with same referenceAlphaId
            const consolidatedPayments = this.consolidatePayments(allPaymentDetailsMapped);
            // Filter out consolidated payments with no financial impact
            // This is necessary because consolidation might result in zero amounts
            const allConsolidatedPayments = consolidatedPayments.filter(payment => (payment.amount && payment.amount > 0) ||
                (payment.creditAmountUsed &&
                    payment.creditAmountUsed > 0) ||
                (payment.creditAmountAdded && payment.creditAmountAdded > 0));
            // Now, total is the length of the filtered consolidated array
            const total = allConsolidatedPayments.length;
            // Apply pagination in memory
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedPaymentDetails = allConsolidatedPayments.slice(startIndex, endIndex);
            // Get owner details
            const ownerDetails = primaryOwner.ownerBrand
                ? {
                    createdAt: primaryOwner.ownerBrand.createdAt,
                    openingBalance: primaryOwner.ownerBrand.openingBalance || 0
                }
                : null;
            const totalPages = Math.ceil(total / limit);
            return {
                paymentDetails: paginatedPaymentDetails,
                total: total,
                ownerDetails,
                totalPages,
                currentPage: page,
                uniqueUsers
            };
        }
        catch (error) {
            console.error(`ERROR in getPaymentDetailsForPatient:`, error);
            throw error;
        }
    }
    /**
     * Get invoices for a specific patient with comprehensive processing
     * @param patientId - The ID of the patient
     * @param search - Optional search term for invoice.reference_alpha_id
     * @param page - Page number for pagination
     * @param limit - Number of items per page
     * @returns Invoices with comprehensive financial reporting
     */
    async getInvoicesForPatient(patientId, search, page = 1, limit = 20, invoiceType) {
        try {
            // Step 1: Build base invoice query for comprehensive reporting
            // Remove owner restriction to fetch invoices from all owners for this patient
            let invoiceQuery = this.invoiceRepository
                .createQueryBuilder('invoice')
                .leftJoinAndMapOne('invoice.creator', 'users', 'user', 'invoice.created_by = user.id')
                .where('invoice.patientId = :patientId', { patientId })
                .andWhere('invoice.invoiceType = :invoiceType', {
                invoiceType: invoiceType || enum_invoice_types_1.EnumInvoiceType.Invoice
            }).andWhere(`(
					CASE
						WHEN '${invoiceType}' = '${enum_invoice_types_1.EnumInvoiceType.Invoice}' THEN
							JSONB_EXTRACT_PATH_TEXT(invoice.metadata::jsonb, 'isOldBalanceInvoice') = 'true'
							OR
							(invoice.details IS NOT NULL AND jsonb_array_length(
								jsonb_path_query_array(invoice.details, '$[*] ? (@.isAddedToCart == true)')
							) > 0)
						ELSE true
					END
				)`);
            // Add search filter for invoice.reference_alpha_id if provided
            if (search && search.trim() !== '') {
                invoiceQuery = invoiceQuery.andWhere('invoice.referenceAlphaId ILIKE :search', {
                    search: `%${search.trim()}%`
                });
            }
            // Get total count for pagination
            const totalCount = await invoiceQuery.getCount();
            // Apply pagination and ordering
            const invoiceResults = await invoiceQuery
                .orderBy('invoice.createdAt', 'DESC')
                .skip((page - 1) * limit)
                .take(limit)
                .getMany();
            // Step 2: Get all invoice IDs for comprehensive payment processing
            const invoiceIds = invoiceResults.map(invoice => invoice.id);
            // Step 3: Fetch comprehensive payment details for all invoices
            const paymentDetailsMap = await this.fetchRelatedPayments(invoiceIds);
            // Step 4: Get appointmentIds for invoices via their cart relationships
            if (invoiceResults.length > 0) {
                const cartIds = invoiceResults
                    .map((invoice) => invoice.cartId)
                    .filter(Boolean);
                if (cartIds.length > 0) {
                    const cartResults = await this.connection.manager
                        .createQueryBuilder()
                        .select([
                        'cart.id as cartId',
                        'cart.appointment_id as appointmentId'
                    ])
                        .from('carts', 'cart')
                        .where('cart.id IN (:...cartIds)', { cartIds })
                        .getRawMany();
                    // Create cart-to-appointment mapping
                    const cartToAppointmentMap = new Map();
                    cartResults.forEach(item => {
                        const cartId = item.cartid || item.cartId;
                        const appointmentId = item.appointmentid || item.appointmentId;
                        if (cartId && appointmentId) {
                            cartToAppointmentMap.set(cartId, appointmentId);
                        }
                    });
                    // Assign appointment IDs to invoices
                    invoiceResults.forEach((invoice) => {
                        const appointmentId = cartToAppointmentMap.get(invoice.cartId) || null;
                        invoice.appointmentId = appointmentId;
                    });
                }
            }
            // Step 5: Fetch comprehensive audit logs
            const auditLogsMap = await this.fetchInvoiceAuditLogs(invoiceIds);
            // Step 6: Build refunded items map from ALL credit notes for this patient
            const refundedItemsMap = new Map();
            // Fetch ALL credit notes for this patient to build a complete refund map
            let allCreditNotes = [];
            try {
                allCreditNotes = await this.invoiceRepository.find({
                    where: {
                        patientId,
                        invoiceType: enum_invoice_types_1.EnumInvoiceType.Refund
                    }
                });
            }
            catch (error) {
                this.logger.error('Error fetching all credit notes for refund calculation', {
                    error,
                    patientId
                });
                allCreditNotes = [];
            }
            // Step 6.1: Identify which invoices are credit notes and create original invoice reference mapping
            const creditNotes = invoiceResults.filter(invoice => invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Refund);
            // Step 6.2: Create a mapping of original invoice references if we have credit notes
            const originalInvoiceReferences = new Map();
            if (creditNotes.length > 0) {
                // Get unique cartIds from credit notes
                const cartIds = [
                    ...new Set(creditNotes
                        .map(creditNote => creditNote.cartId)
                        .filter(Boolean))
                ];
                if (cartIds.length > 0) {
                    try {
                        // Step 6.2a: Find original invoices that match these cartIds
                        const originalInvoices = await this.invoiceRepository.find({
                            where: {
                                cartId: (0, typeorm_2.In)(cartIds),
                                invoiceType: enum_invoice_types_1.EnumInvoiceType.Invoice,
                                patientId // Ensure we only get invoices for this patient
                            },
                            select: [
                                'id',
                                'cartId',
                                'referenceAlphaId',
                                'referenceId'
                            ]
                        });
                        // Step 6.2b: Create mapping from cartId to original invoice reference
                        originalInvoices.forEach(invoice => {
                            const referenceId = invoice.referenceAlphaId ||
                                (invoice.referenceId
                                    ? String(invoice.referenceId)
                                    : '');
                            if (invoice.cartId && referenceId) {
                                originalInvoiceReferences.set(invoice.cartId, referenceId);
                            }
                        });
                    }
                    catch (error) {
                        this.logger.error('Error fetching original invoices for credit notes', {
                            error,
                            cartIds,
                            patientId
                        });
                        // Continue without original references if this fails
                    }
                }
            }
            // Process all credit notes to build the refunded items map
            // and create a map of original invoice IDs to their credit notes
            const invoiceToCreditNotesMap = new Map();
            allCreditNotes.forEach(creditNote => {
                if (creditNote.cartId && creditNote.details) {
                    const cartId = creditNote.cartId;
                    const itemMap = refundedItemsMap.get(cartId) ||
                        new Map();
                    const details = Array.isArray(creditNote.details)
                        ? creditNote.details
                        : [];
                    details.forEach(item => {
                        const detailItem = item;
                        if (detailItem && detailItem.id) {
                            const refundedQty = Number(detailItem.quantity || 0);
                            itemMap.set(detailItem.id, (itemMap.get(detailItem.id) || 0) + refundedQty);
                        }
                    });
                    refundedItemsMap.set(cartId, itemMap);
                }
                // Build credit notes mapping for refund cards
                if (creditNote.cartId) {
                    // Find the original invoice that matches this credit note's cartId
                    const originalInvoice = invoiceResults.find(invoice => invoice.cartId === creditNote.cartId &&
                        invoice.invoiceType !== enum_invoice_types_1.EnumInvoiceType.Refund);
                    if (originalInvoice) {
                        const creditNoteInfo = {
                            id: creditNote.id,
                            referenceAlphaId: creditNote.referenceAlphaId || '',
                            amount: Number(creditNote.invoiceAmount || 0),
                            createdAt: creditNote.createdAt
                        };
                        const existingCreditNotes = invoiceToCreditNotesMap.get(originalInvoice.id) ||
                            [];
                        existingCreditNotes.push(creditNoteInfo);
                        invoiceToCreditNotesMap.set(originalInvoice.id, existingCreditNotes);
                    }
                }
            });
            // Step 7: Process invoices with comprehensive financial data
            const processedInvoices = invoiceResults.map(invoice => {
                const creator = invoice.creator;
                const createdByName = creator && (creator.firstName || creator.lastName)
                    ? `${creator.firstName || ''} ${creator.lastName || ''}`.trim()
                    : 'System';
                // Get payments for this invoice
                const relatedPayments = paymentDetailsMap.get(invoice.id) || [];
                const processedPayments = this.processRelatedPayments(relatedPayments);
                // Calculate comprehensive financial totals
                const totalPaidAmount = processedPayments.reduce((sum, payment) => sum + (payment.amount || 0), 0);
                const invoiceAmount = invoice.invoiceAmount || 0;
                const totalPrice = invoice.totalPrice || 0;
                // Convert processed payments to PaymentDetail format
                const paymentDetails = processedPayments.map(payment => ({
                    id: payment.id,
                    amount: payment.amount,
                    paymentType: payment.paymentType,
                    paymentMode: payment.paymentMode,
                    createdAt: payment.createdAt,
                    createdBy: payment.createdBy,
                    createdByName: payment.createdByName,
                    isCreditUsed: payment.isCreditUsed,
                    creditAmountUsed: payment.creditAmountUsed,
                    referenceAlphaId: payment.referenceAlphaId
                }));
                // Get audit logs for this invoice
                const auditLogs = auditLogsMap.get(invoice.id) || [];
                // Get credit notes for this invoice (for refund cards)
                const creditNotes = invoiceToCreditNotesMap.get(invoice.id) || [];
                // Determine if this is a credit note and find its original invoice reference
                let originalInvoiceReferenceAlphaId = null;
                if (invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Refund) {
                    // Check if we have a mapping for this cartId
                    if (invoice.cartId &&
                        originalInvoiceReferences.has(invoice.cartId)) {
                        originalInvoiceReferenceAlphaId =
                            originalInvoiceReferences.get(invoice.cartId) ||
                                null;
                    }
                }
                // Add refund information to invoice details
                let processedDetails = invoice.details || [];
                if (invoice.cartId &&
                    refundedItemsMap.has(invoice.cartId)) {
                    const refundedItems = refundedItemsMap.get(invoice.cartId);
                    processedDetails = processedDetails.map(item => {
                        const detailItem = item;
                        // Only items that are added to cart can be refunded
                        if (detailItem.isAddedToCart) {
                            const refundedQty = detailItem.id
                                ? refundedItems.get(detailItem.id) || 0
                                : 0;
                            const originalQty = Number(detailItem.quantity || 0);
                            const remainingQty = Math.max(0, originalQty - refundedQty);
                            return {
                                ...item,
                                refundedQuantity: refundedQty,
                                remainingRefundQuantity: remainingQty,
                                canBeRefunded: remainingQty > 0
                            };
                        }
                        else {
                            // For items not added to cart, set refund fields to 0 and canBeRefunded to false
                            return {
                                ...item,
                                refundedQuantity: 0,
                                remainingRefundQuantity: 0,
                                canBeRefunded: false
                            };
                        }
                    });
                }
                else {
                    // No refunds for this invoice, set default refund values
                    processedDetails = processedDetails.map(item => {
                        const detailItem = item;
                        const originalQty = Number(detailItem.quantity || 0);
                        return {
                            ...item,
                            refundedQuantity: 0,
                            remainingRefundQuantity: detailItem.isAddedToCart ? originalQty : 0,
                            canBeRefunded: detailItem.isAddedToCart && originalQty > 0
                        };
                    });
                }
                return {
                    id: invoice.id,
                    referenceAlphaId: invoice.referenceAlphaId || '',
                    invoiceAmount,
                    totalCollected: totalPaidAmount,
                    totalprice: totalPrice,
                    discountAmount: invoice.totalDiscount || 0,
                    details: processedDetails, // Use processed details with refund info
                    balanceDue: invoice.balanceDue || 0,
                    status: invoice.status,
                    createdAt: invoice.createdAt,
                    createdBy: invoice.createdBy || '',
                    createdByName,
                    patientId: invoice.patientId,
                    comment: '',
                    metadata: invoice.metadata || {},
                    payments: paymentDetails,
                    cartId: invoice.cartId,
                    discount: invoice.discount,
                    auditLogs: auditLogs, // Add audit logs information
                    appointmentId: invoice.appointmentId || null, // Add appointment ID information
                    creditNotes: creditNotes, // Add credit notes information for refund cards
                    originalInvoiceReferenceAlphaId // Add the original invoice reference for credit notes
                };
            });
            // Step 8: Calculate comprehensive summary
            const summary = {
                totalAmount: processedInvoices.reduce((sum, inv) => sum + inv.invoiceAmount, 0),
                totalPaidAmount: processedInvoices.reduce((sum, inv) => sum + inv.totalCollected, 0),
                totalBalanceAmount: processedInvoices.reduce((sum, inv) => sum + inv.balanceDue, 0),
                totalCreditAmount: processedInvoices.reduce((sum, inv) => sum +
                    inv.payments.reduce((creditSum, payment) => creditSum + payment.creditAmountUsed, 0), 0)
            };
            const totalPages = Math.ceil(totalCount / limit);
            this.logger.log('Processed comprehensive invoices for patient successfully', {
                patientId,
                totalCount,
                processedCount: processedInvoices.length,
                totalPages,
                currentPage: page,
                summary
            });
            return {
                invoices: processedInvoices,
                total: totalCount,
                pagination: {
                    total: totalCount,
                    page,
                    limit,
                    hasMore: page < totalPages
                },
                summary
            };
        }
        catch (error) {
            this.logger.error('Error fetching comprehensive invoices for patient', {
                error: error.message,
                stack: error.stack,
                patientId,
                search
            });
            // Return empty response instead of throwing error
            // This ensures the API doesn't fail when no data is found
            return {
                invoices: [],
                total: 0,
                pagination: {
                    total: 0,
                    page,
                    limit,
                    hasMore: false
                },
                summary: {
                    totalAmount: 0,
                    totalPaidAmount: 0,
                    totalBalanceAmount: 0,
                    totalCreditAmount: 0
                }
            };
        }
    }
    async getPaymentDetailsForOwner(ownerId, filters) {
        // Step 1: Validate the owner exists
        const owner = await this.ownerBrandRepository.findOne({
            where: { id: ownerId }
        });
        if (!owner) {
            return {
                paymentDetails: [],
                total: 0,
                ownerDetails: null,
                uniqueUsers: []
            };
        }
        try {
            // Basic where conditions
            const whereConditions = { ownerId };
            const queryParams = { ownerId };
            // Create an array to hold additional where clauses for complex conditions
            const additionalWhereConditions = [];
            // Apply date range filters
            if (filters === null || filters === void 0 ? void 0 : filters.startDate) {
                additionalWhereConditions.push('payment.created_at >= :startDate');
                queryParams.startDate = new Date(filters.startDate);
            }
            if (filters === null || filters === void 0 ? void 0 : filters.endDate) {
                additionalWhereConditions.push('payment.created_at <= :endDate');
                queryParams.endDate = new Date(filters.endDate);
            }
            // Apply payment mode/type filters
            if (filters === null || filters === void 0 ? void 0 : filters.paymentMode) {
                // Check if it's a comma-separated list of payment modes
                if (filters.paymentMode.includes(',')) {
                    // Split the comma-separated string into an array of payment modes
                    const paymentModes = filters.paymentMode
                        .split(',')
                        .map(mode => mode.trim());
                    // Create OR conditions for each payment mode
                    const paymentModeConditions = paymentModes.map((_, index) => `payment.payment_type = :paymentMode${index}`);
                    // Add the OR conditions to the query
                    additionalWhereConditions.push(`(${paymentModeConditions.join(' OR ')})`);
                    // Add each payment mode as a separate parameter
                    paymentModes.forEach((mode, index) => {
                        queryParams[`paymentMode${index}`] = mode;
                    });
                }
                else {
                    // Single payment mode
                    whereConditions.paymentType = filters.paymentMode;
                }
            }
            // Apply payment type filters
            if (filters === null || filters === void 0 ? void 0 : filters.paymentType) {
                whereConditions.type = filters.paymentType;
            }
            // Apply creator/user ID filter
            // The DTO uses `userId` (singular) which can now accept a comma-separated string of IDs.
            if ((filters === null || filters === void 0 ? void 0 : filters.userId) && filters.userId.trim() !== '') {
                const userIdString = filters.userId.trim(); // Changed from userIds to userId to match DTO
                if (userIdString.includes(',')) {
                    // Split the comma-separated string into an array of user IDs
                    const userIdsArray = userIdString
                        .split(',')
                        .map((id) => id.trim())
                        .filter((id) => id !== '');
                    if (userIdsArray.length > 0) {
                        // Use TypeORM's In operator for multiple user IDs
                        whereConditions.createdBy = (0, typeorm_2.In)(userIdsArray);
                    }
                }
                else {
                    // Single user ID
                    whereConditions.createdBy = userIdString;
                }
            }
            // Get the base query with the simple where conditions
            let query = this.paymentDetailsRepository
                .createQueryBuilder('payment')
                .leftJoinAndSelect('payment.invoice', 'invoice')
                .leftJoinAndSelect('payment.patient', 'patient')
                .leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
                .leftJoinAndMapOne('payment.creator', 'users', 'user', 'payment.created_by = user.id')
                .where(whereConditions)
                // Exclude soft-deleted rows, write-off invoice entries, and hidden rows
                .andWhere('payment.deleted_at IS NULL')
                .andWhere('payment.type != :writeOffType', { writeOffType: enum_credit_types_1.EnumAmountType.WriteOffInvoice })
                .andWhere('payment.showininvoice = true');
            // Add complex conditions
            if (additionalWhereConditions.length > 0) {
                query = query.andWhere(additionalWhereConditions.join(' AND '), queryParams);
            }
            // Apply pet name filter
            if (filters === null || filters === void 0 ? void 0 : filters.petName) {
                query = query.andWhere('patient.patient_name LIKE :petName', {
                    petName: `%${filters.petName}%`
                });
            }
            // Apply search filter
            if ((filters === null || filters === void 0 ? void 0 : filters.searchTerm) && filters.searchTerm.trim() !== '') {
                const searchTerm = `%${filters.searchTerm.toLowerCase()}%`;
                // Try to parse as date
                let dateParam = null;
                try {
                    const possibleDate = new Date(filters.searchTerm);
                    if (!isNaN(possibleDate.getTime())) {
                        dateParam = possibleDate;
                    }
                }
                catch (_a) {
                    // Not a date, continue with text search
                }
                if (dateParam) {
                    // Date search
                    const startOfDay = new Date(dateParam);
                    startOfDay.setHours(0, 0, 0, 0);
                    const endOfDay = new Date(dateParam);
                    endOfDay.setHours(23, 59, 59, 999);
                    query = query.andWhere('(payment.created_at BETWEEN :startDate AND :endDate)', {
                        startDate: startOfDay,
                        endDate: endOfDay
                    });
                }
                else {
                    // Text search
                    query = query.andWhere(`(
						LOWER(payment.reference_alpha_id) LIKE :searchTerm OR
						LOWER(COALESCE(patient.patient_name, '')) LIKE :searchTerm OR
						LOWER(payment.payment_type) LIKE :searchTerm OR
						LOWER(payment.type) LIKE :searchTerm OR
						LOWER(COALESCE(user.first_name, '')) LIKE :searchTerm OR
						LOWER(COALESCE(user.last_name, '')) LIKE :searchTerm OR
						LOWER(COALESCE(invoice.reference_alpha_id, '')) LIKE :searchTerm
					)`, { searchTerm });
                }
            }
            // Filter out empty invoices
            query = query.andWhere(`(
				payment.type != 'Invoice' OR
				(invoice.details IS NOT NULL AND jsonb_array_length(
					jsonb_path_query_array(invoice.details, '$[*] ? (@.isAddedToCart == true)')
				) > 0)
			)`);
            // Filter out entries with no financial impact
            query = query.andWhere(`(
				payment.amount > 0 OR
				payment.credit_amount_used > 0 OR
				payment.credit_amount_added > 0
			)`);
            // Explicitly select the created_at column with an alias for sorting
            // This helps TypeORM resolve the column correctly in complex queries.
            query = query.addSelect('payment.created_at', 'payment_order_by_created_at');
            // Apply order by with explicit column name
            query = query.orderBy('payment_order_by_created_at', 'DESC');
            // Execute the query to get ALL matching results (no DB pagination yet)
            const allPaymentDetailsResults = await query.getMany();
            // Fetch unique users who created payment details for this owner
            let uniqueUsers = [];
            try {
                // Get the base query with only owner filter - no other filters applied
                const usersQuery = this.paymentDetailsRepository
                    .createQueryBuilder('payment')
                    .select([
                    'DISTINCT user.id AS id',
                    'user.first_name AS firstname',
                    'user.last_name AS lastname'
                ])
                    .leftJoin('users', 'user', 'payment.created_by = user.id')
                    .where('payment.owner_id = :ownerId', { ownerId })
                    .andWhere(`(
						payment.amount > 0 OR
						payment.credit_amount_used > 0 OR
						payment.credit_amount_added > 0
					)`);
                const users = await usersQuery.getRawMany();
                // Format user data for frontend - using lowercase column names from raw query
                uniqueUsers = users
                    .map(user => ({
                    id: user.id,
                    name: `${user.firstname || ''} ${user.lastname || ''}`.trim() ||
                        'Staff Member'
                }))
                    // Filter out users named 'Staff Member'
                    .filter(user => user.name !== 'Staff Member');
            }
            catch (error) {
                this.logger.error('Error fetching unique users for payment details', {
                    error,
                    ownerId
                });
                uniqueUsers = []; // Use empty array on error
            }
            if (allPaymentDetailsResults.length === 0) {
                return {
                    paymentDetails: [],
                    total: 0,
                    ownerDetails: {
                        createdAt: owner.createdAt,
                        openingBalance: owner.openingBalance || 0
                    },
                    uniqueUsers // This will be an empty array if no payments matched, which is correct.
                };
            }
            // Map results and add createdByName
            const allPaymentDetailsMapped = allPaymentDetailsResults.map(payment => {
                const result = payment;
                const creator = payment.creator;
                result.createdByName =
                    creator && (creator.firstName || creator.lastName)
                        ? `${creator.firstName || ''} ${creator.lastName || ''}`.trim()
                        : result.createdBy
                            ? 'Staff Member'
                            : 'System';
                return result;
            });
            // Consolidate payments with same referenceAlphaId
            const allConsolidatedPayments = this.consolidatePayments(allPaymentDetailsMapped);
            // Now, total is the length of the consolidated array
            const total = allConsolidatedPayments.length;
            // Apply pagination in memory
            let paginatedPayments = allConsolidatedPayments;
            if ((filters === null || filters === void 0 ? void 0 : filters.page) && (filters === null || filters === void 0 ? void 0 : filters.limit)) {
                const skip = (filters.page - 1) * filters.limit;
                paginatedPayments = allConsolidatedPayments.slice(skip, skip + filters.limit);
            }
            // Return the result
            return {
                paymentDetails: paginatedPayments, // PAGINATED CONSOLIDATED ITEMS
                total: total, // CORRECTED POST-CONSOLIDATION COUNT
                ownerDetails: {
                    createdAt: owner.createdAt,
                    openingBalance: owner.openingBalance || 0
                },
                uniqueUsers // This now correctly reflects users from filtered results.
            };
        }
        catch (error) {
            console.error(`ERROR in getPaymentDetailsForOwner:`, error);
            throw error;
        }
    }
    // Helper Functions
    /** Fetch the primary owner for a patient */
    async getPrimaryOwner(patientId) {
        const primaryOwner = await this.patientOwnerRepository.findOne({
            where: { patientId, isPrimary: true },
            relations: ['ownerBrand']
        });
        if (!primaryOwner) {
            this.logger.warn(`No primary owner found for patient ID: ${patientId}`);
        }
        return primaryOwner;
    }
    /** Build a map of refunded quantities per item by cartId from credit notes */
    buildRefundedItemsMap(paymentDetails) {
        const refundedItemsMap = new Map();
        paymentDetails.forEach(pd => {
            var _a;
            if (pd.type === 'Credit Note' && ((_a = pd.invoice) === null || _a === void 0 ? void 0 : _a.cartId)) {
                const cartId = pd.invoice.cartId;
                const itemMap = refundedItemsMap.get(cartId) || new Map();
                (pd.invoice.details || []).forEach(item => {
                    const detailItem = item;
                    if (detailItem.id) {
                        const refundedQty = Number(detailItem.quantity || 0);
                        itemMap.set(detailItem.id, (itemMap.get(detailItem.id) || 0) + refundedQty);
                    }
                });
                refundedItemsMap.set(cartId, itemMap);
            }
        });
        return refundedItemsMap;
    }
    /** Consolidate payments with the same referenceAlphaId */
    consolidatePayments(paymentDetails) {
        const paymentsByRef = new Map();
        const nonConsolidatedPayments = [];
        // Group payments by referenceAlphaId
        paymentDetails.forEach(pd => {
            if (pd.referenceAlphaId) {
                const group = paymentsByRef.get(pd.referenceAlphaId) || [];
                group.push(pd);
                paymentsByRef.set(pd.referenceAlphaId, group);
            }
            else {
                nonConsolidatedPayments.push(pd);
            }
        });
        // Consolidate each group
        const consolidatedPayments = Array.from(paymentsByRef.values())
            .map(group => {
            // Sort group by creation date to ensure consistent ordering
            const sortedGroup = [...group].sort((a, b) => {
                var _a, _b;
                // Primary sort by patientId presence
                if (a.patientId && !b.patientId)
                    return -1;
                if (!a.patientId && b.patientId)
                    return 1;
                // Secondary sort by type to put Reconcile Invoice and Collect in a predictable order
                const typeOrder = {
                    'Reconcile Invoice': 0,
                    Collect: 1,
                    Invoice: 2,
                    'Credit Note': 3,
                    Return: 4
                };
                const aTypeOrder = (_a = typeOrder[a.type]) !== null && _a !== void 0 ? _a : 999;
                const bTypeOrder = (_b = typeOrder[b.type]) !== null && _b !== void 0 ? _b : 999;
                if (aTypeOrder !== bTypeOrder) {
                    return aTypeOrder - bTypeOrder;
                }
                // Finally sort by creation date (oldest first to maintain consistent order)
                return (new Date(a.createdAt).getTime() -
                    new Date(b.createdAt).getTime());
            });
            const basePayment = {
                ...sortedGroup[0]
            };
            // If base payment is of type 'Collect', change it to 'ReconcileInvoice'
            if (basePayment.type === 'Collect' && sortedGroup.length > 1) {
                basePayment.type = enum_credit_types_1.EnumAmountType.ReconcileInvoice;
            }
            basePayment.amount = group.reduce((sum, p) => sum + Number(p.amount || 0), 0);
            basePayment.creditAmountUsed = group.reduce((sum, p) => sum + Number(p.creditAmountUsed || 0), 0);
            // Handle credits added across collect payments
            const creditsAddedPayments = group.filter(p => p.isCreditsAdded);
            if (creditsAddedPayments.length > 0) {
                basePayment.isCreditsAdded = true;
                basePayment.creditAmountAdded = creditsAddedPayments.reduce((sum, p) => sum + Number(p.creditAmountAdded || 0), 0);
            }
            // Always add relatedPayments, even for single items
            basePayment.relatedPayments = sortedGroup
                .filter(p => p.type !== 'Collect') // Filter out Collect entries
                .map(p => ({
                ...p,
                invoiceReferenceAlphaId: p.referenceAlphaId
            }));
            // Handle specific fields for 'Collect' payments if present
            const collectPayments = group.filter(p => p.type === 'Collect');
            if (collectPayments.length > 0) {
                basePayment.collectPayments = collectPayments.map(p => ({
                    id: p.id,
                    amount: p.amount,
                    createdAt: p.createdAt,
                    ledgerDocumentFilekey: p.ledgerDocumentFilekey
                }));
                basePayment.hasCollectPayment = true;
                basePayment.collectAmount = collectPayments.reduce((sum, p) => sum + Number(p.amount || 0), 0);
            }
            return [basePayment];
        })
            .flat();
        // For non-consolidated payments, add them with their own relatedPayments
        const enhancedNonConsolidatedPayments = nonConsolidatedPayments.map((payment) => {
            const enhancedPayment = {
                ...payment,
                relatedPayments: [
                    {
                        ...payment,
                        invoiceReferenceAlphaId: payment.referenceAlphaId
                    }
                ]
            };
            return enhancedPayment;
        });
        // Combine all payments
        const allPayments = [
            ...enhancedNonConsolidatedPayments,
            ...consolidatedPayments
        ];
        // Group by invoiceId for special ordering
        const paymentsByInvoiceId = new Map();
        const paymentsWithoutInvoice = [];
        allPayments.forEach(payment => {
            if (payment.invoiceId) {
                const invoiceGroup = paymentsByInvoiceId.get(payment.invoiceId) || [];
                invoiceGroup.push(payment);
                paymentsByInvoiceId.set(payment.invoiceId, invoiceGroup);
            }
            else {
                paymentsWithoutInvoice.push(payment);
            }
        });
        // Process each invoice group to ensure Reconcile Invoice comes before Invoice
        const processedPayments = [];
        paymentsByInvoiceId.forEach(group => {
            // Sort the group so that "Reconcile Invoice" comes before "Invoice"
            const sortedGroup = [...group].sort((a, b) => {
                if (a.type === 'Reconcile Invoice' && b.type === 'Invoice')
                    return -1;
                if (a.type === 'Invoice' && b.type === 'Reconcile Invoice')
                    return 1;
                // If neither case applies, maintain original ordering by timestamp
                return (new Date(b.createdAt).getTime() -
                    new Date(a.createdAt).getTime());
            });
            processedPayments.push(...sortedGroup);
        });
        // Add payments without invoice
        processedPayments.push(...paymentsWithoutInvoice);
        // Final sort by timestamp for payments that aren't part of the same invoice
        return processedPayments.sort((a, b) => {
            // If they have the same invoiceId, the ordering has already been handled above
            if (a.invoiceId && b.invoiceId && a.invoiceId === b.invoiceId) {
                return 0; // Preserve the order established earlier
            }
            // If they have the same referenceAlphaId, keep the earlier grouped order
            if (a.referenceAlphaId &&
                b.referenceAlphaId &&
                a.referenceAlphaId === b.referenceAlphaId) {
                return 0;
            }
            // Otherwise sort by timestamp
            return (new Date(b.createdAt).getTime() -
                new Date(a.createdAt).getTime());
        });
    }
    /** Fetch all related payments for given invoice IDs in one query */
    async fetchRelatedPayments(invoiceIds) {
        if (!invoiceIds.length)
            return new Map();
        const query = this.paymentDetailsRepository
            .createQueryBuilder('payment')
            .leftJoin('users', 'user', 'payment.created_by = user.id')
            .leftJoin('patients', 'patient', 'payment.patient_id = patient.id')
            .select([
            'payment.id AS payment_id',
            'payment.amount AS payment_amount',
            'payment.type AS type',
            'payment.payment_type AS payment_type',
            'payment.created_at AS payment_created_at',
            'payment.created_by AS payment_created_by',
            'payment.is_credit_used AS payment_is_credit_used',
            'payment.credit_amount_used AS payment_credit_amount_used',
            'payment.reference_alpha_id AS payment_reference_alpha_id',
            'payment.reference_id AS payment_reference_id',
            'payment.invoice_id AS invoice_id',
            'payment.patient_id AS patient_id',
            'user.first_name AS user_first_name',
            'user.last_name AS user_last_name',
            'patient.patient_name AS patient_name'
        ])
            .where('payment.invoice_id IN (:...invoiceIds)', { invoiceIds })
            .andWhere('payment.deleted_at IS NULL')
            .andWhere('payment.type != :writeOffType', { writeOffType: enum_credit_types_1.EnumAmountType.WriteOffInvoice })
            .andWhere('payment.showininvoice = true')
            .orderBy('payment.created_at', 'ASC');
        const payments = await query.getRawMany();
        const grouped = new Map();
        payments.forEach(p => {
            const invoiceId = p.invoice_id;
            const group = grouped.get(invoiceId) || [];
            group.push(p);
            grouped.set(invoiceId, group);
        });
        return grouped;
    }
    /** Process related payments, consolidating bulk payments within them */
    processRelatedPayments(payments) {
        const paymentObjs = payments.map(p => ({
            id: p.payment_id,
            amount: Number(p.payment_amount || 0),
            paymentType: p.type || '',
            paymentMode: p.payment_type || '',
            createdAt: p.payment_created_at,
            createdBy: p.payment_created_by || '',
            createdByName: p.user_first_name && p.user_last_name
                ? `${p.user_first_name} ${p.user_last_name}`.trim()
                : p.payment_created_by
                    ? 'Staff Member'
                    : 'System',
            isCreditUsed: !!p.payment_is_credit_used,
            creditAmountUsed: Number(p.payment_credit_amount_used || 0),
            referenceAlphaId: p.payment_reference_alpha_id ||
                (p.payment_reference_id ? String(p.payment_reference_id) : ''),
            patientId: p.patient_id || null,
            patientName: p.patient_name || null
        }));
        const groupedByRef = new Map();
        const nonBulkPayments = [];
        paymentObjs.forEach(p => {
            if (p.paymentType === enum_credit_types_1.EnumAmountType.BulkReconcileInvoice &&
                p.referenceAlphaId) {
                const group = groupedByRef.get(p.referenceAlphaId) || [];
                group.push(p);
                groupedByRef.set(p.referenceAlphaId, group);
            }
            else {
                nonBulkPayments.push(p);
            }
        });
        const consolidatedBulk = Array.from(groupedByRef.values()).map(group => {
            const bulkPayment = { ...group[0] };
            bulkPayment.totalAmount = group.reduce((sum, p) => sum + p.amount, 0);
            bulkPayment.totalCreditAmount = group.reduce((sum, p) => sum + p.creditAmountUsed, 0);
            bulkPayment.individualPayments = group;
            bulkPayment.isBulkPayment = true;
            return bulkPayment;
        });
        return [...nonBulkPayments, ...consolidatedBulk].sort((a, b) => new Date(a.createdAt).getTime() -
            new Date(b.createdAt).getTime());
    }
    /** Add refund information to invoice details */
    addRefundInfoToInvoiceDetails(details, refundedItems) {
        return details.map(item => {
            const detailItem = item;
            const refundedQty = detailItem.id
                ? refundedItems.get(detailItem.id) || 0
                : 0;
            const originalQty = Number(detailItem.quantity || 0);
            const remainingQty = Math.max(0, originalQty - refundedQty);
            return {
                ...item,
                refundedQuantity: refundedQty,
                remainingRefundQuantity: remainingQty,
                canBeRefunded: remainingQty > 0
            };
        });
    }
    /** Duplicate invoices and credit notes as 'Reconcile Invoice' */
    addReconcileInvoices(paymentDetails) {
        const result = [];
        paymentDetails.forEach(pd => {
            if ((pd.type === 'Invoice' || pd.type === 'Credit Note') &&
                (pd.amount > 0 || pd.creditAmountUsed > 0)) {
                const reconcilePd = {
                    ...pd,
                    type: enum_credit_types_1.EnumAmountType.ReconcileInvoice
                };
                delete reconcilePd.payments;
                result.push(reconcilePd);
            }
            result.push(pd);
        });
        return result;
    }
    async generatePdfForReceipt(receiptData, fileKey) {
        try {
            const receiptHTML = (0, receipt_1.generateReceipt)(receiptData);
            const receiptBuffer = await (0, generatePdf_1.generatePDF)(receiptHTML);
            await this.s3Service.uploadPdfToS3(receiptBuffer, fileKey);
            this.loggerService.log('successfully generated pdf for payment');
            return { receiptBuffer };
        }
        catch (error) {
            this.loggerService.log('error during generating pdf', error);
            return { receiptBuffer: null };
        }
    }
    async findOne(id) {
        const response = await this.paymentDetailsRepository.findOne({
            where: { id }
        });
        if (!response) {
            throw new common_1.NotFoundException(`Not found entity for requested id: ${id}`);
        }
        return response;
    }
    async deleteLedgerFileKey(id) {
        const paymentDetail = await this.paymentDetailsRepository.findOne({
            where: { id }
        });
        if (!paymentDetail) {
            throw new common_1.NotFoundException(`Not found entity for requested id: ${id}`);
        }
        if (paymentDetail.ledgerDocumentFilekey) {
            await this.s3Service.deleteFile(paymentDetail.ledgerDocumentFilekey);
            await this.paymentDetailsRepository.update(id, {
                ledgerDocumentFilekey: ''
            });
        }
    }
    async getOwnerInvoicesWithPayments(ownerId, filters, userId, brandId, clinicId, page = 1, limit = 10) {
        try {
            // 1. Get owner details
            const owner = await this.ownerBrandRepository.findOne({
                where: { id: ownerId }
            });
            if (!owner) {
                throw new common_1.NotFoundException(`Owner not found with ID: ${ownerId}`);
            }
            // Add the computeOwnerBalance method first
            const ownerBalance = await this.computeOwnerBalance(owner.id);
            const ownerDetails = {
                id: owner.id,
                name: `${owner.firstName || ''} ${owner.lastName || ''}`.trim(),
                balance: ownerBalance, // Use computed balance instead of stored value
                credits: Number(owner.ownerCredits || 0)
            };
            let uniqueUsers = [];
            try {
                const usersQuery = this.invoiceRepository
                    .createQueryBuilder('invoice')
                    .select([
                    'DISTINCT user.id AS id',
                    'user.first_name AS firstName',
                    'user.last_name AS lastName'
                ])
                    .innerJoin('users', 'user', 'invoice.created_by = user.id')
                    .where('invoice.ownerId = :ownerId', { ownerId })
                    .andWhere('invoice.brandId = :brandId', { brandId })
                    .andWhere('invoice.clinicId = :clinicId', { clinicId })
                    .andWhere('invoice.invoiceType = :invoiceType', {
                    invoiceType: filters.invoiceType || enum_invoice_types_1.EnumInvoiceType.Invoice
                });
                const users = await usersQuery.getRawMany();
                // Format user data for frontend
                uniqueUsers = users.map(user => ({
                    id: user.id,
                    name: `${user.firstname || ''} ${user.lastname || ''}`.trim() ||
                        'Staff Member'
                }));
            }
            catch (error) {
                this.logger.error('Error fetching unique users', {
                    error,
                    ownerId
                });
                uniqueUsers = []; // Use empty array on error
            }
            // Extract and sanitize search term
            const searchTerm = filters.searchTerm || '';
            const hasSearchTerm = searchTerm.trim() !== '';
            // 2. Find invoices for this owner directly
            let invoices = [];
            let totalInvoices = 0;
            try {
                // Create a single unified query that searches everything at once
                const combinedQuery = this.invoiceRepository
                    .createQueryBuilder('invoice')
                    .leftJoin('patients', 'patient', 'invoice.patientId = patient.id')
                    .leftJoin('users', 'user', 'invoice.created_by = user.id')
                    .where('invoice.ownerId = :ownerId', { ownerId })
                    .andWhere('invoice.brandId = :brandId', { brandId })
                    .andWhere('invoice.clinicId = :clinicId', { clinicId })
                    .andWhere('invoice.invoiceType = :invoiceType', {
                    invoiceType: filters.invoiceType || enum_invoice_types_1.EnumInvoiceType.Invoice
                }).andWhere(`(
						CASE
							WHEN '${filters.invoiceType}' = '${enum_invoice_types_1.EnumInvoiceType.Invoice}' THEN
								JSONB_EXTRACT_PATH_TEXT(invoice.metadata::jsonb, 'isOldBalanceInvoice') = 'true'
								OR
								(invoice.details IS NOT NULL AND jsonb_array_length(
									jsonb_path_query_array(invoice.details, '$[*] ? (@.isAddedToCart == true)')
								) > 0)
							ELSE true
						END
					)`);
                // Apply filters
                if (filters.startDate) {
                    combinedQuery.andWhere('invoice.createdAt >= :startDate', {
                        startDate: new Date(filters.startDate)
                    });
                }
                if (filters.endDate) {
                    combinedQuery.andWhere('invoice.createdAt <= :endDate', {
                        endDate: new Date(filters.endDate)
                    });
                }
                if (filters.status) {
                    // Check if status is comma-separated list of statuses
                    const statuses = filters.status
                        .split(',')
                        .map(s => s.trim())
                        .filter(Boolean);
                    if (statuses.length > 1) {
                        combinedQuery.andWhere('invoice.status IN (:...statuses)', { statuses });
                    }
                    else {
                        combinedQuery.andWhere('invoice.status = :status', {
                            status: filters.status
                        });
                    }
                }
                // Filter by pet name if provided (exact match)
                if (filters.petName) {
                    combinedQuery.andWhere('patient.patient_name = :petName', {
                        petName: filters.petName
                    });
                }
                // Filter by creator/user ID if provided
                if (filters.userId) {
                    const userIds = filters.userId
                        .split(',')
                        .map(id => id.trim())
                        .filter(Boolean);
                    if (userIds.length > 0) {
                        combinedQuery.andWhere('invoice.created_by IN (:...userIds)', { userIds });
                    }
                }
                // Apply search term with explicit debugging
                if (hasSearchTerm) {
                    // Convert searchTerm to lowercase and add % wildcards for LIKE
                    const formattedTerm = `%${searchTerm.toLowerCase()}%`;
                    combinedQuery.andWhere(`(
							LOWER(invoice.reference_alpha_id) LIKE :searchTerm OR
							LOWER(COALESCE(patient.patient_name, '')) LIKE :searchTerm OR
							LOWER(invoice.status::text) LIKE :searchTerm OR
							LOWER(COALESCE(user.first_name, '')) LIKE :searchTerm OR
							LOWER(COALESCE(user.last_name, '')) LIKE :searchTerm
						)`, { searchTerm: formattedTerm });
                }
                // Get total count before pagination
                totalInvoices = await combinedQuery.getCount();
                // Add pagination - get normal invoice entities first
                invoices = await combinedQuery
                    .orderBy('invoice.createdAt', 'DESC')
                    .skip((page - 1) * limit)
                    .take(limit)
                    .getMany();
                // Get appointmentIds for invoices via their cart relationships
                if (invoices.length > 0) {
                    const cartIds = invoices
                        .map(invoice => invoice.cartId)
                        .filter(Boolean);
                    if (cartIds.length > 0) {
                        const cartResults = await this.connection.manager
                            .createQueryBuilder()
                            .select([
                            'cart.id as cartId',
                            'cart.appointment_id as appointmentId'
                        ])
                            .from('carts', 'cart')
                            .where('cart.id IN (:...cartIds)', { cartIds })
                            .getRawMany();
                        // Create cart-to-appointment mapping
                        const cartToAppointmentMap = new Map();
                        cartResults.forEach(item => {
                            const cartId = item.cartid || item.cartId;
                            const appointmentId = item.appointmentid || item.appointmentId;
                            if (cartId && appointmentId) {
                                cartToAppointmentMap.set(cartId, appointmentId);
                            }
                        });
                        // Assign appointment IDs to invoices
                        invoices = invoices.map(invoice => {
                            const appointmentId = cartToAppointmentMap.get(invoice.cartId) || null;
                            invoice.appointmentId = appointmentId;
                            return invoice;
                        });
                    }
                }
            }
            catch (error) {
                this.logger.error('Error fetching invoices', {
                    error,
                    ownerId,
                    searchTerm
                });
                // Return empty results instead of failing completely
                return {
                    ownerDetails,
                    invoices: [],
                    uniqueUsers,
                    pagination: {
                        total: 0,
                        page,
                        limit,
                        hasMore: false
                    }
                };
            }
            if (invoices.length === 0) {
                return {
                    ownerDetails,
                    invoices: [],
                    uniqueUsers,
                    pagination: {
                        total: totalInvoices,
                        page,
                        limit,
                        hasMore: false
                    }
                };
            }
            // Fetch audit logs for all invoices in a single query for efficiency
            const invoiceIds = invoices.map(invoice => invoice.id);
            const auditLogsMap = await this.fetchInvoiceAuditLogs(invoiceIds);
            // Step 1: Identify which invoices are credit notes
            const creditNotes = invoices.filter(invoice => invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Refund);
            // Step 2: Create a mapping of original invoice references if we have credit notes
            const originalInvoiceReferences = new Map();
            if (creditNotes.length > 0) {
                // Step 2a: Collect unique cartIds from credit notes that have them
                const cartIds = creditNotes
                    .filter(creditNote => creditNote.cartId) // Only include credit notes with a cartId
                    .map(creditNote => creditNote.cartId); // Ensure cartId is treated as string
                if (cartIds.length > 0) {
                    try {
                        // Step 2b: Find all original invoices that match these cartIds
                        const originalInvoicesQuery = this.invoiceRepository
                            .createQueryBuilder('invoice')
                            .select([
                            'invoice.id',
                            'invoice.cartId',
                            'invoice.referenceAlphaId',
                            'invoice.referenceId'
                        ])
                            .where('invoice.cartId IN (:...cartIds)', {
                            cartIds
                        })
                            .andWhere('invoice.invoiceType = :invoiceType', {
                            invoiceType: enum_invoice_types_1.EnumInvoiceType.Invoice
                        }) // Original invoice type
                            .andWhere('invoice.ownerId = :ownerId', { ownerId })
                            .andWhere('invoice.brandId = :brandId', { brandId })
                            .andWhere('invoice.clinicId = :clinicId', {
                            clinicId
                        });
                        const originalInvoices = await originalInvoicesQuery.getMany();
                        // Step 2c: Create mapping from cartId to original invoice reference
                        originalInvoices.forEach(invoice => {
                            const referenceId = invoice.referenceAlphaId ||
                                (invoice.referenceId
                                    ? String(invoice.referenceId)
                                    : '');
                            if (invoice.cartId && referenceId) {
                                originalInvoiceReferences.set(invoice.cartId, referenceId);
                            }
                        });
                    }
                    catch (error) {
                        this.logger.error('Error fetching original invoices for credit notes', {
                            error,
                            cartIds
                        });
                        // Continue without original references if this fails
                    }
                }
            }
            // 3. Process invoices
            const invoiceResults = [];
            // If we have a search term and no direct matches were found initially,
            // we'll need to check each invoice for pet name match
            // Build refunded items map from ALL credit notes for this owner, not just the ones in the current page
            const refundedItemsMap = new Map();
            // Fetch ALL credit notes for this owner to build a complete refund map
            let allCreditNotes = [];
            try {
                allCreditNotes = await this.invoiceRepository.find({
                    where: {
                        ownerId,
                        brandId,
                        clinicId,
                        invoiceType: enum_invoice_types_1.EnumInvoiceType.Refund
                    }
                });
                this.logger.log('Fetched all credit notes for refund calculation', {
                    ownerId,
                    count: allCreditNotes.length
                });
            }
            catch (error) {
                this.logger.error('Error fetching all credit notes for refund calculation', {
                    error,
                    ownerId
                });
                // Continue with the credit notes we have from the current page
                allCreditNotes = creditNotes;
            }
            // Create a map of original invoice IDs to their credit notes
            const invoiceToCreditNotesMap = new Map();
            // Process all credit notes to build the refunded items map
            // and create a map of original invoice IDs to their credit notes
            allCreditNotes.forEach(creditNote => {
                if (creditNote.cartId) {
                    // Logic to build refundedItemsMap
                    if (creditNote.details) {
                        const cartId = creditNote.cartId;
                        const itemMap = refundedItemsMap.get(cartId) ||
                            new Map();
                        const details = Array.isArray(creditNote.details)
                            ? creditNote.details
                            : [];
                        details.forEach(item => {
                            const detailItem = item;
                            if (detailItem && detailItem.id) {
                                const refundedQty = Number(detailItem.quantity || 0);
                                itemMap.set(detailItem.id, (itemMap.get(detailItem.id) || 0) +
                                    refundedQty);
                            }
                        });
                        refundedItemsMap.set(cartId, itemMap);
                    }
                    // Logic to build invoiceToCreditNotesMap
                    const originalInvoice = invoices.find(invoice => invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice &&
                        invoice.cartId === creditNote.cartId);
                    if (originalInvoice) {
                        const creditNoteInfo = {
                            id: creditNote.id,
                            referenceAlphaId: creditNote.referenceAlphaId || '',
                            amount: Number(creditNote.invoiceAmount || 0),
                            createdAt: creditNote.createdAt
                        };
                        const existingCreditNotes = invoiceToCreditNotesMap.get(originalInvoice.id) ||
                            [];
                        existingCreditNotes.push(creditNoteInfo);
                        invoiceToCreditNotesMap.set(originalInvoice.id, existingCreditNotes);
                    }
                }
            });
            for (const invoice of invoices) {
                try {
                    // Check if this is an old balance invoice
                    // Commenting out unused variable
                    // const isOldBalanceInvoice = invoice?.metadata?.isOldBalanceInvoice === true;
                    // We've already filtered empty details arrays at the database level
                    // Get payments for this invoice
                    let payments = [];
                    try {
                        // TypeORM requires proper entity relations
                        const paymentQuery = this.paymentDetailsRepository
                            .createQueryBuilder('payment')
                            .leftJoinAndSelect('payment.invoice', 'invoice')
                            .leftJoinAndSelect('payment.patient', 'patient')
                            .leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
                            .leftJoinAndMapOne('payment.creator', 'users', 'user', 'payment.created_by = user.id')
                            .select([
                            'payment.id AS payment_id',
                            'payment.amount AS payment_amount',
                            'payment.type AS type',
                            'payment.payment_type AS payment_type',
                            'payment.created_at AS payment_created_at',
                            'payment.created_by AS payment_created_by',
                            'payment.main_balance AS payment_main_balance',
                            'payment.is_credit_used AS payment_is_credit_used',
                            'payment.credit_amount_used AS payment_credit_amount_used',
                            'payment.reference_alpha_id AS payment_reference_alpha_id',
                            'payment.reference_id AS payment_reference_id',
                            'user.id AS user_id',
                            'user.first_name AS user_first_name',
                            'user.last_name AS user_last_name'
                        ])
                            .where('payment.invoice_id = :invoiceId', {
                            invoiceId: invoice.id
                        })
                            .andWhere('payment.deleted_at IS NULL')
                            .andWhere('payment.type != :writeOffType', { writeOffType: enum_credit_types_1.EnumAmountType.WriteOffInvoice })
                            .andWhere('payment.showininvoice = true');
                        if (filters.paymentMode) {
                            paymentQuery.andWhere('payment.payment_type = :paymentMode', { paymentMode: filters.paymentMode });
                        }
                        // Use getRawMany to get the raw database results with aliases
                        payments = await paymentQuery
                            .orderBy('payment.created_at', 'ASC')
                            .getRawMany();
                    }
                    catch (error) {
                        // Just log the error and continue with empty payments array
                        this.logger.error('Error fetching payments', {
                            error,
                            invoiceId: invoice.id
                        });
                        payments = []; // Use empty array on error
                    }
                    // Skip invoices with no payments if payment mode filter is applied
                    if (filters.paymentMode && payments.length === 0) {
                        continue;
                    }
                    // Transform payment data
                    const paymentDetails = payments.map((payment) => ({
                        id: payment.payment_id,
                        amount: Number(payment.payment_amount || 0),
                        paymentType: payment.type || '',
                        paymentMode: payment.payment_type || '',
                        createdAt: payment.payment_created_at,
                        createdBy: payment.payment_created_by || '',
                        createdByName: payment.user_first_name &&
                            payment.user_last_name
                            ? `${payment.user_first_name} ${payment.user_last_name}`.trim()
                            : payment.payment_created_by
                                ? 'Staff Member'
                                : 'System',
                        isCreditUsed: !!payment.payment_is_credit_used,
                        creditAmountUsed: Number(payment.payment_credit_amount_used || 0),
                        referenceAlphaId: payment.payment_reference_alpha_id ||
                            (payment.payment_reference_id
                                ? String(payment.payment_reference_id)
                                : '')
                    }));
                    // Group payments by referenceAlphaId for bulk payments
                    const groupedPayments = new Map();
                    paymentDetails.forEach(payment => {
                        if (payment.paymentType ===
                            enum_credit_types_1.EnumAmountType.BulkReconcileInvoice &&
                            payment.referenceAlphaId) {
                            if (!groupedPayments.has(payment.referenceAlphaId)) {
                                groupedPayments.set(payment.referenceAlphaId, []);
                            }
                            const paymentGroup = groupedPayments.get(payment.referenceAlphaId);
                            if (paymentGroup) {
                                paymentGroup.push(payment);
                            }
                        }
                    });
                    // Process the grouped payments
                    const combinedPaymentDetails = [];
                    // First add non-bulk payments directly
                    paymentDetails.forEach(payment => {
                        if (payment.paymentType !==
                            enum_credit_types_1.EnumAmountType.BulkReconcileInvoice) {
                            combinedPaymentDetails.push(payment);
                        }
                    });
                    // Then add combined bulk payments
                    groupedPayments.forEach(payments => {
                        if (payments.length > 0) {
                            // Use the first payment as the base
                            const baseBulkPayment = { ...payments[0] };
                            // Combine the amounts from all payments with this referenceAlphaId
                            baseBulkPayment.totalAmount = payments.reduce((sum, p) => sum + p.amount, 0);
                            baseBulkPayment.totalCreditAmount = payments.reduce((sum, p) => sum + p.creditAmountUsed, 0);
                            // Add related payments with invoice reference IDs
                            baseBulkPayment.relatedPayments = payments.map(p => {
                                // Get invoice reference alpha ID or look it up if needed
                                let invoiceRefAlphaId = p.referenceAlphaId;
                                // If we don't have the reference alpha ID, try to extract it from related data
                                if (!invoiceRefAlphaId &&
                                    p.invoice &&
                                    p.invoice.referenceAlphaId) {
                                    invoiceRefAlphaId =
                                        p.invoice.referenceAlphaId;
                                }
                                return {
                                    ...p,
                                    invoiceReferenceAlphaId: invoiceRefAlphaId
                                };
                            });
                            // Update description to indicate bulk payment
                            baseBulkPayment.bulkPayment = true;
                            combinedPaymentDetails.push(baseBulkPayment);
                        }
                    });
                    // Get invoice creator info
                    let invoiceCreatorName = 'System';
                    try {
                        if (invoice.createdBy) {
                            // Try to get user information by ID
                            const userQuery = this.connection.manager
                                .createQueryBuilder()
                                .select([
                                'user.id',
                                'user.first_name',
                                'user.last_name'
                            ])
                                .from('users', 'user')
                                .where('user.id = :userId', {
                                userId: invoice.createdBy
                            });
                            const creator = await userQuery.getRawOne();
                            if (creator &&
                                (creator.first_name || creator.last_name)) {
                                invoiceCreatorName =
                                    `${creator.first_name || ''} ${creator.last_name || ''}`.trim();
                            }
                            else {
                                invoiceCreatorName = 'Staff Member';
                            }
                        }
                    }
                    catch (error) {
                        this.logger.error('Error fetching invoice creator', {
                            error,
                            invoiceId: invoice.id
                        });
                    }
                    // Get patient name
                    let patientName = 'Unknown';
                    try {
                        if (invoice.patientId) {
                            const patient = await this.patientService.findOne(invoice.patientId);
                            if (patient && patient.patientName) {
                                patientName = patient.patientName;
                            }
                        }
                    }
                    catch (error) {
                        this.logger.error('Error fetching patient', {
                            error,
                            patientId: invoice.patientId
                        });
                    }
                    // Determine if this is a credit note and find its original invoice reference
                    let originalInvoiceReferenceAlphaId = null;
                    if (invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Refund) {
                        // Check if we have a mapping for this cartId
                        if (invoice.cartId &&
                            originalInvoiceReferences.has(invoice.cartId)) {
                            originalInvoiceReferenceAlphaId =
                                originalInvoiceReferences.get(invoice.cartId) ||
                                    null;
                        }
                    }
                    // Process invoice details to add refund information if this is a regular invoice
                    let processedDetails = invoice.details;
                    // Only process details for regular invoices, not credit notes
                    if (invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice &&
                        invoice.cartId &&
                        Array.isArray(invoice.details)) {
                        // Get refunded items for this cart if they exist
                        const refundedItems = refundedItemsMap.get(invoice.cartId) ||
                            new Map();
                        // Always add refund information to each item in the invoice details
                        // even if there are no refunds yet
                        processedDetails = invoice.details.map(item => {
                            const detailItem = item;
                            // Only items that are added to cart can be refunded
                            if (detailItem.isAddedToCart) {
                                const refundedQty = detailItem.id
                                    ? refundedItems.get(detailItem.id) || 0
                                    : 0;
                                const originalQty = Number(detailItem.quantity || 0);
                                const remainingQty = Math.max(0, originalQty - refundedQty);
                                return {
                                    ...item,
                                    refundedQuantity: refundedQty,
                                    remainingRefundQuantity: remainingQty,
                                    canBeRefunded: remainingQty > 0
                                };
                            }
                            else {
                                // For items not added to cart, set refund fields to 0 and canBeRefunded to false
                                return {
                                    ...item,
                                    refundedQuantity: 0,
                                    remainingRefundQuantity: 0,
                                    canBeRefunded: false
                                };
                            }
                        });
                    }
                    // Get credit notes for this invoice if it's a regular invoice
                    let creditNotes = [];
                    if (invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Invoice) {
                        creditNotes =
                            invoiceToCreditNotesMap.get(invoice.id) || [];
                    }
                    // Get audit logs for this invoice
                    const auditLogs = auditLogsMap.get(invoice.id) || [];
                    // Add invoice to results
                    invoiceResults.push({
                        id: invoice.id,
                        referenceAlphaId: invoice.referenceAlphaId ||
                            (invoice.referenceId
                                ? String(invoice.referenceId)
                                : ''),
                        invoiceAmount: Number(invoice.invoiceAmount || 0),
                        totalCollected: Number(invoice.paidAmount || 0),
                        totalprice: Number(invoice.totalPrice || 0),
                        discountAmount: Number(invoice.totalDiscount || 0),
                        cartId: invoice.cartId || '',
                        appointmentId: invoice.appointmentId || '', // Add appointmentId from cart
                        details: processedDetails || '',
                        balanceDue: Number(invoice.balanceDue || 0),
                        status: invoice.status || 'PENDING',
                        createdAt: invoice.createdAt,
                        createdBy: invoice.createdBy || '',
                        createdByName: invoiceCreatorName,
                        patientId: invoice.patientId || '',
                        patientName,
                        comment: '', // Added placeholder comment field
                        metadata: invoice.metadata || {},
                        originalInvoiceReferenceAlphaId, // Add the new field
                        discount: Number(invoice.discount || 0), // Add discount percentage
                        payments: combinedPaymentDetails,
                        creditNotes: creditNotes, // Add credit notes information
                        auditLogs: auditLogs // Add audit logs information
                    });
                }
                catch (error) {
                    this.logger.error('Error processing invoice', {
                        error,
                        invoiceId: invoice.id
                    });
                }
            }
            // Calculate if there are more results
            const hasMore = totalInvoices > page * limit;
            return {
                ownerDetails,
                invoices: invoiceResults,
                uniqueUsers,
                pagination: {
                    total: totalInvoices,
                    page,
                    limit,
                    hasMore
                }
            };
        }
        catch (error) {
            this.logger.error('Error in getOwnerInvoicesWithPayments', {
                error,
                ownerId
            });
            throw error;
        }
    }
    async getOwnerLedger(ownerId, userId, brandId, clinicId) {
        try {
            // 1. Get owner details
            const owner = await this.ownerBrandRepository.findOne({
                where: { id: ownerId }
            });
            if (!owner) {
                throw new common_1.NotFoundException(`Owner not found with ID: ${ownerId}`);
            }
            // IMPORTANT NEW STEP: Check for the existence of an "old balance invoice" that consolidates previous entries
            let consolidationDate = null;
            let hasConsolidationInvoice = false;
            try {
                // Find the earliest invoice with isOldBalanceInvoice=true for this owner
                const oldBalanceInvoice = await this.invoiceRepository
                    .createQueryBuilder('invoice')
                    .where('invoice.ownerId = :ownerId', { ownerId })
                    .andWhere('invoice.brandId = :brandId', { brandId })
                    .andWhere('invoice.clinicId = :clinicId', { clinicId })
                    .andWhere("JSONB_EXTRACT_PATH_TEXT(invoice.metadata::jsonb, 'isOldBalanceInvoice') = 'true'")
                    .orderBy('invoice.createdAt', 'ASC')
                    .getOne();
                if (oldBalanceInvoice) {
                    hasConsolidationInvoice = true;
                    consolidationDate = oldBalanceInvoice.createdAt;
                }
            }
            catch (error) {
                this.logger.error('Error checking for consolidation invoice', {
                    error,
                    ownerId
                });
                // Continue without filtering if there's an error in this step
            }
            // 2. Get payment details
            const paymentDetailsResult = await this.getPaymentDetailsForOwner(ownerId);
            // 3. Get invoices
            const invoicesResult = await this.getOwnerInvoicesWithPayments(ownerId, { invoiceType: enum_invoice_types_1.EnumInvoiceType.Invoice }, // Regular Invoices
            userId, brandId, clinicId, 1, 10000 // Assuming a large enough limit to fetch all for ledger
            );
            // 4. Get credit notes (Refund Invoices)
            const creditnoteResult = await this.getOwnerInvoicesWithPayments(ownerId, { invoiceType: enum_invoice_types_1.EnumInvoiceType.Refund }, // Refund Invoices (Credit Notes)
            userId, brandId, clinicId, 1, 10000 // Assuming a large enough limit
            );
            // 5. Combine and transform into LedgerItems
            let ledgerItems = [];
            if (paymentDetailsResult.paymentDetails &&
                paymentDetailsResult.paymentDetails.length > 0) {
                const paymentItems = paymentDetailsResult.paymentDetails.map(payment => {
                    const enhancedPayment = payment;
                    let monetaryCredit = 0;
                    let monetaryDebit = 0;
                    let itemDisplayType = payment.type || '';
                    let currentCreditChange = 0;
                    const paymentAmountNumber = Number(payment.amount) || 0;
                    const creditAmountUsedNumber = Number(payment.creditAmountUsed) || 0;
                    const creditAmountAddedNumber = Number(payment.creditAmountAdded) || 0;
                    if (payment.type === enum_credit_types_1.EnumAmountType.Collect) {
                        if (payment.isCreditsAdded &&
                            creditAmountAddedNumber > 0) {
                            if (payment.invoiceId) {
                                monetaryDebit = creditAmountAddedNumber;
                                monetaryCredit = 0;
                                currentCreditChange =
                                    creditAmountAddedNumber;
                                itemDisplayType = 'Credits Added';
                            }
                            else {
                                monetaryDebit = 0;
                                monetaryCredit = 0;
                                currentCreditChange =
                                    creditAmountAddedNumber;
                                itemDisplayType = 'Credits Added';
                            }
                            // THIS IS THE KEY CHANGE:
                            // Money collected/allocated specifically to add to profile credits.
                            // This transaction line itself is neutral to the monetary running balance.
                            // The cash inflow (if new money) is for the credit pool, not general balance.
                            // Or, if from excess (e.g. CN), the monetary impact was handled by the CN itself.
                        }
                        else {
                            // A 'Collect' type not adding to credits is unusual.
                            // If it happens, treat as a general monetary collection.
                            monetaryCredit = paymentAmountNumber;
                            currentCreditChange = paymentAmountNumber;
                            itemDisplayType = 'Credits Added'; // Clarify display type
                        }
                    }
                    else if (payment.type === enum_credit_types_1.EnumAmountType.Return) {
                        // Cash is going OUT of the business to the customer
                        itemDisplayType = 'Cash Refund'; // Default type
                        if (payment.isCreditUsed &&
                            creditAmountUsedNumber > 0) {
                            // Cash returned by cashing out existing profile credits
                            currentCreditChange = -creditAmountUsedNumber; // Profile credits deducted
                            itemDisplayType = 'Credits Returned';
                            // monetaryDebit still stands as cash is physically returned.
                        }
                    }
                    else if (payment.type === enum_credit_types_1.EnumAmountType.ReconcileInvoice ||
                        payment.type ===
                            enum_credit_types_1.EnumAmountType.BulkReconcileInvoice ||
                        payment.type === enum_credit_types_1.EnumAmountType.Invoice // Payment against an invoice
                    ) {
                        itemDisplayType = 'Invoice Cleared';
                        // Cash/Card/etc. coming IN against an invoice
                        if (paymentAmountNumber > 0 &&
                            !(payment.isCreditUsed &&
                                payment.paymentType ===
                                    enum_payment_types_1.EnumPaymentType.Credits)) {
                            monetaryCredit = paymentAmountNumber;
                        }
                        // If profile credits are used to pay part of the invoice:
                        if (payment.isCreditUsed &&
                            creditAmountUsedNumber > 0) {
                            currentCreditChange = -creditAmountUsedNumber; // Profile credits used
                            monetaryCredit = +creditAmountUsedNumber;
                            // No direct monetaryCredit for the creditAmountUsed part on this line
                        }
                        if (payment.isCreditsAdded &&
                            creditAmountAddedNumber > 0) {
                            currentCreditChange = creditAmountAddedNumber;
                            monetaryCredit =
                                paymentAmountNumber -
                                    creditAmountAddedNumber;
                            itemDisplayType += ' & Credits Added';
                        }
                    }
                    else if (payment.type === enum_credit_types_1.EnumAmountType.CreditNote) {
                        // This is a PaymentDetailsEntity with type 'CreditNote',
                        // representing cash paid out for a refund related to a Credit Note invoice.
                        monetaryDebit = paymentAmountNumber; // Cash paid out
                        itemDisplayType = 'Amount Returned';
                    }
                    else if (payment.type === enum_credit_types_1.EnumAmountType.WriteOffInvoice) {
                        // Write-off adjustments reduce the outstanding balance without affecting profile credits
                        itemDisplayType = 'Invoice Write-off';
                        monetaryCredit = paymentAmountNumber; // Credit entry to offset the debit from original invoice
                        currentCreditChange = 0;
                        monetaryDebit = 0;
                    }
                    return {
                        id: payment.id,
                        type: itemDisplayType,
                        referenceAlphaId: payment.referenceAlphaId,
                        amount: paymentAmountNumber, // This is the 'amount' field of the payment_details record
                        debit: monetaryDebit,
                        credit: monetaryCredit,
                        runningBalance: null,
                        creditChange: currentCreditChange,
                        runningCredits: null,
                        createdAt: payment.createdAt,
                        createdBy: payment.createdBy,
                        createdByName: enhancedPayment.createdByName || 'System',
                        paymentType: payment.paymentType,
                        paymentMode: payment.paymentType,
                        patientId: payment.patientId,
                        patientName: payment.patient
                            ? `${payment.patient.patientName || ''}`.trim()
                            : '',
                        notes: payment.paymentNotes || '',
                        isCreditUsed: payment.isCreditUsed,
                        creditAmountUsed: creditAmountUsedNumber,
                        source: 'payment',
                        // Populate clearedInvoices for payment entries
                        clearedInvoices: (() => {
                            var _a, _b;
                            const cleared = [];
                            // Check if relatedPayments exists AND is not an empty array
                            if (enhancedPayment.relatedPayments &&
                                enhancedPayment.relatedPayments.length > 0) {
                                // Iterate through the individual payments that were consolidated
                                enhancedPayment.relatedPayments.forEach(
                                // Optional chaining ?. is not strictly needed here due to the improved if-condition, but doesn't harm
                                relatedPayment => {
                                    if (relatedPayment.invoice) {
                                        cleared.push({
                                            invoiceId: relatedPayment.invoice
                                                .id, // Assuming id is always present if invoice object exists
                                            invoiceReferenceAlphaId: relatedPayment.invoice
                                                .referenceAlphaId,
                                            amountApplied: Number(relatedPayment.amount ||
                                                0) +
                                                Number(relatedPayment.creditAmountUsed ||
                                                    0) // Amount applied by this specific related payment
                                        });
                                    }
                                });
                            }
                            else {
                                // This block will now also run if enhancedPayment.relatedPayments is an empty array
                                // or if enhancedPayment.invoice exists (even if it's null, the properties will be handled)
                                if (enhancedPayment.invoice ||
                                    enhancedPayment.type === 'Collect' ||
                                    enhancedPayment.type === 'Return') {
                                    // Added a check to ensure we only push if there's an invoice OR it's a type that might not have one but still needs an entry
                                    cleared.push({
                                        invoiceId: ((_a = enhancedPayment.invoice) === null || _a === void 0 ? void 0 : _a.id) ||
                                            '', // Fallback to empty string if invoice or id is null/undefined
                                        invoiceReferenceAlphaId: (_b = enhancedPayment.invoice) === null || _b === void 0 ? void 0 : _b.referenceAlphaId,
                                        amountApplied: Number(enhancedPayment.amount || 0) +
                                            Number(enhancedPayment.creditAmountUsed ||
                                                0) // Amount applied by the main enhancedPayment
                                    });
                                }
                            }
                            return cleared.length > 0 ? cleared : undefined;
                        })()
                    };
                });
                ledgerItems.push(...paymentItems);
            }
            if (invoicesResult.invoices && invoicesResult.invoices.length > 0) {
                const invoiceItems = [];
                invoicesResult.invoices.forEach(invoice => {
                    var _a, _b;
                    const invoiceAmountNumber = Number(invoice.invoiceAmount) || 0;
                    // Check if this is a balance consolidation invoice
                    const isOldBalanceInvoice = invoice.metadata &&
                        typeof invoice.metadata === 'object' &&
                        invoice.metadata.isOldBalanceInvoice === true;
                    // Check if this invoice is written off or cancelled
                    const isWriteoffInvoice = invoice.status === enum_invoice_status_1.EnumInvoiceStatus.WRITTEN_OFF;
                    const isCancelledInvoice = invoice.status === enum_invoice_status_1.EnumInvoiceStatus.CANCELLED;
                    // Check for writeoff information in metadata for partial write-offs
                    let writeoffInfo = null;
                    let isPartialWriteoff = false;
                    let isCompleteWriteoff = false;
                    if (invoice.metadata &&
                        typeof invoice.metadata === 'object') {
                        // Check for writeoff metadata (for both complete and partial writeoffs)
                        if (invoice.metadata.writeoff &&
                            typeof invoice.metadata.writeoff === 'object') {
                            writeoffInfo = invoice.metadata.writeoff;
                            const writeoffAmount = Number(writeoffInfo.amount) || 0;
                            const paidAmount = Number(invoice.totalCollected) || 0;
                            // Determine if it's a partial or complete writeoff
                            // Partial writeoff: some amount was paid before writeoff
                            // Complete writeoff: no payments made, entire amount written off
                            if (paidAmount > 0 && writeoffAmount > 0) {
                                isPartialWriteoff = true;
                                isCompleteWriteoff = false;
                            }
                            else if (paidAmount === 0 && writeoffAmount > 0) {
                                isPartialWriteoff = false;
                                isCompleteWriteoff = true;
                            }
                        }
                    }
                    // Set the appropriate display type
                    let displayType;
                    if (isOldBalanceInvoice) {
                        displayType = 'Previous Balance Consolidated';
                    }
                    else {
                        displayType = 'Invoice Generated';
                    }
                    // For balance consolidation invoices, add a comment if not already present
                    const invoiceComment = isOldBalanceInvoice
                        ? invoice.comment ||
                            'Consolidation of previous negative balance'
                        : invoice.comment || '';
                    // Create the main invoice ledger item
                    const invoiceItem = {
                        id: invoice.id,
                        type: displayType,
                        referenceAlphaId: invoice.referenceAlphaId,
                        amount: invoiceAmountNumber,
                        // Complete writeoff and cancelled invoices should not appear as debits
                        // Partial writeoff invoices should show full debit (writeoff will offset separately)
                        debit: invoiceAmountNumber,
                        credit: 0,
                        runningBalance: null,
                        creditChange: 0, // Invoices themselves don't change credits; payments do
                        runningCredits: null,
                        balanceDue: Number(invoice.balanceDue) || 0,
                        totalCollected: Number(invoice.totalCollected) || 0,
                        createdAt: invoice.createdAt,
                        createdBy: invoice.createdBy,
                        createdByName: invoice.createdByName || 'System',
                        patientId: invoice.patientId,
                        patientName: invoice.patientName || '',
                        comment: invoiceComment,
                        status: invoice.status,
                        source: 'invoice',
                        isConsolidationInvoice: isOldBalanceInvoice,
                        isWriteoffInvoice: isWriteoffInvoice ||
                            isCompleteWriteoff ||
                            isPartialWriteoff, // Add flag to identify any writeoff invoices
                        isCancelledInvoice: isCancelledInvoice // Add flag to identify cancelled invoices
                    };
                    invoiceItems.push(invoiceItem);
                    // Create separate ledger entries for writeoffs and cancellations
                    if (isPartialWriteoff && writeoffInfo) {
                        // Partial writeoff entry
                        const writeoffAmount = Number(writeoffInfo.amount) || 0;
                        const writeoffDate = writeoffInfo.date
                            ? new Date(writeoffInfo.date)
                            : invoice.createdAt;
                        const writeoffBy = writeoffInfo.by || 'System';
                        const writeoffReason = writeoffInfo.reason || 'Partial writeoff';
                        const writeoffLedgerItem = {
                            id: `writeoff-${invoice.id}`, // Unique ID for writeoff entry
                            type: 'Partial Writeoff',
                            referenceAlphaId: `WO-${invoice.referenceAlphaId}`, // Prefix with WO for writeoff
                            amount: writeoffAmount,
                            debit: 0, // Writeoff reduces the amount owed (credit to customer)
                            credit: writeoffAmount, // Credit entry to balance the partial writeoff
                            runningBalance: null,
                            creditChange: 0, // Writeoffs don't affect profile credits
                            runningCredits: null,
                            balanceDue: 0, // Not applicable for writeoff entries
                            totalCollected: 0, // Not applicable for writeoff entries
                            createdAt: writeoffDate,
                            createdBy: writeoffInfo.userId || invoice.createdBy,
                            createdByName: writeoffBy,
                            patientId: invoice.patientId,
                            patientName: invoice.patientName || '',
                            comment: writeoffReason,
                            status: 'WRITTEN_OFF',
                            source: 'invoice', // Still related to invoice
                            notes: `Partial writeoff of ${writeoffAmount} from invoice ${invoice.referenceAlphaId}`
                        };
                        invoiceItems.push(writeoffLedgerItem);
                        this.logger.log('Created partial writeoff ledger entry', {
                            invoiceId: invoice.id,
                            invoiceReferenceAlphaId: invoice.referenceAlphaId,
                            writeoffAmount,
                            writeoffDate,
                            writeoffBy,
                            writeoffReason
                        });
                    }
                    else if (isCompleteWriteoff && writeoffInfo) {
                        // Complete writeoff entry
                        const writeoffAmount = Number(writeoffInfo.amount) || 0;
                        const writeoffDate = writeoffInfo.date
                            ? new Date(writeoffInfo.date)
                            : invoice.createdAt;
                        const writeoffBy = writeoffInfo.by || 'System';
                        const writeoffReason = writeoffInfo.reason || 'Complete writeoff';
                        const writeoffLedgerItem = {
                            id: `writeoff-${invoice.id}`, // Unique ID for writeoff entry
                            type: 'Complete Writeoff',
                            referenceAlphaId: `WO-${invoice.referenceAlphaId}`, // Prefix with WO for writeoff
                            amount: writeoffAmount,
                            debit: 0, // Writeoff reduces the amount owed (credit to customer)
                            credit: writeoffAmount, // Credit entry to balance the complete writeoff
                            runningBalance: null,
                            creditChange: 0, // Writeoffs don't affect profile credits
                            runningCredits: null,
                            balanceDue: 0, // Not applicable for writeoff entries
                            totalCollected: 0, // Not applicable for writeoff entries
                            createdAt: writeoffDate,
                            createdBy: writeoffInfo.userId || invoice.createdBy,
                            createdByName: writeoffBy,
                            patientId: invoice.patientId,
                            patientName: invoice.patientName || '',
                            comment: writeoffReason,
                            status: 'WRITTEN_OFF',
                            source: 'invoice', // Still related to invoice
                            notes: `Complete writeoff of ${writeoffAmount} from invoice ${invoice.referenceAlphaId}`
                        };
                        invoiceItems.push(writeoffLedgerItem);
                        this.logger.log('Created complete writeoff ledger entry', {
                            invoiceId: invoice.id,
                            invoiceReferenceAlphaId: invoice.referenceAlphaId,
                            writeoffAmount,
                            writeoffDate,
                            writeoffBy,
                            writeoffReason
                        });
                    }
                    else if (isCancelledInvoice) {
                        // Invoice cancellation entry
                        const cancellationDate = invoice.createdAt; // Use created date as base
                        // Try to get cancellation info from metadata if available
                        let cancellationBy = 'System';
                        let cancellationReason = 'Invoice cancelled';
                        let cancellationActualDate = cancellationDate;
                        if (invoice.metadata &&
                            typeof invoice.metadata === 'object') {
                            if (invoice.metadata.cancellation) {
                                cancellationBy =
                                    invoice.metadata.cancellation.by ||
                                        'System';
                                cancellationReason =
                                    invoice.metadata.cancellation.reason ||
                                        'Invoice cancelled';
                                // Use cancellation date from metadata if available
                                if (invoice.metadata.cancellation.date) {
                                    cancellationActualDate = new Date(invoice.metadata.cancellation.date);
                                }
                            }
                        }
                        const cancellationLedgerItem = {
                            id: `cancellation-${invoice.id}`, // Unique ID for cancellation entry
                            type: 'Invoice Cancellation',
                            referenceAlphaId: `IC-${invoice.referenceAlphaId}`, // Prefix with CN for cancellation
                            amount: invoiceAmountNumber,
                            debit: 0, // Cancellation removes the debt obligation
                            credit: invoiceAmountNumber, // Credit entry to balance the cancellation
                            runningBalance: null,
                            creditChange: 0, // Cancellations don't affect profile credits
                            runningCredits: null,
                            balanceDue: 0, // Not applicable for cancellation entries
                            totalCollected: 0, // Not applicable for cancellation entries
                            createdAt: cancellationActualDate,
                            createdBy: ((_b = (_a = invoice.metadata) === null || _a === void 0 ? void 0 : _a.cancellation) === null || _b === void 0 ? void 0 : _b.userId) ||
                                invoice.createdBy,
                            createdByName: cancellationBy,
                            patientId: invoice.patientId,
                            patientName: invoice.patientName || '',
                            comment: cancellationReason,
                            status: 'CANCELLED',
                            source: 'invoice', // Still related to invoice
                            notes: `Invoice cancellation of ${invoiceAmountNumber} for invoice ${invoice.referenceAlphaId}`
                        };
                        invoiceItems.push(cancellationLedgerItem);
                        this.logger.log('Created invoice cancellation ledger entry', {
                            invoiceId: invoice.id,
                            invoiceReferenceAlphaId: invoice.referenceAlphaId,
                            cancellationAmount: invoiceAmountNumber,
                            cancellationDate: cancellationActualDate,
                            cancellationBy,
                            cancellationReason
                        });
                    }
                });
                ledgerItems.push(...invoiceItems);
            }
            if (creditnoteResult.invoices &&
                creditnoteResult.invoices.length > 0) {
                // These are InvoiceEntities with invoiceType: EnumInvoiceType.Refund
                const creditNoteInvoiceItems = creditnoteResult.invoices.map(cnInvoice => {
                    const cnAmountNumber = Number(cnInvoice.invoiceAmount) || 0;
                    return {
                        id: cnInvoice.id,
                        type: 'Credit Note Generated', // Indicates a credit note was created
                        referenceAlphaId: cnInvoice.referenceAlphaId,
                        amount: cnAmountNumber,
                        debit: 0,
                        credit: cnAmountNumber, // A credit note issued increases owner's balance (reduces liability or customer debt)
                        runningBalance: null,
                        creditChange: 0, // The CN itself doesn't change profile credits; associated payments do
                        runningCredits: null,
                        balanceDue: Number(cnInvoice.balanceDue) || 0,
                        totalCollected: Number(cnInvoice.totalCollected) || 0, // How much of this CN was "paid out" or applied
                        createdAt: cnInvoice.createdAt,
                        createdBy: cnInvoice.createdBy,
                        createdByName: cnInvoice.createdByName || 'System',
                        patientId: cnInvoice.patientId,
                        patientName: cnInvoice.patientName || '',
                        comment: cnInvoice.comment || '',
                        status: cnInvoice.status,
                        source: 'creditnote'
                    };
                });
                ledgerItems.push(...creditNoteInvoiceItems);
            }
            // NEW STEP: Filter out entries created before the consolidation invoice if it exists
            if (hasConsolidationInvoice && consolidationDate) {
                const originalItemCount = ledgerItems.length;
                ledgerItems = ledgerItems.filter(item => {
                    const itemDate = new Date(item.createdAt);
                    return itemDate >= consolidationDate;
                });
                this.logger.log('Filtered ledger items before consolidation date', {
                    ownerId,
                    consolidationDate,
                    originalItemCount,
                    filteredItemCount: ledgerItems.length,
                    removedItems: originalItemCount - ledgerItems.length
                });
            }
            // REVISED APPROACH:
            // For consolidation invoices, the opening balance was already migrated to either:
            // 1. Credits (for positive balances) - handled via credit transactions
            // 2. Consolidation invoices (for negative balances) - shown as invoices with isOldBalanceInvoice=true
            // Only add an explicit opening balance entry if:
            // - No consolidation invoice exists (i.e., this is a new owner created after the migration)
            // - The owner has a non-zero opening balance value
            const openingBalanceAmount = Number(owner.openingBalance) || 0;
            if (!hasConsolidationInvoice && openingBalanceAmount !== 0) {
                const openingBalanceDate = new Date(owner.createdAt.getTime() - 1000); // Just before owner creation
                const openingBalanceEntry = {
                    id: 'opening-balance-' + ownerId,
                    type: 'Opening Balance',
                    referenceAlphaId: '',
                    amount: Math.abs(openingBalanceAmount),
                    // Depending on whether opening balance is positive or negative:
                    debit: openingBalanceAmount < 0
                        ? Math.abs(openingBalanceAmount)
                        : 0,
                    credit: openingBalanceAmount > 0 ? openingBalanceAmount : 0,
                    runningBalance: openingBalanceAmount, // Initial running balance
                    creditChange: 0, // Opening balance doesn't affect credits
                    runningCredits: 0, // Credits start at 0
                    createdAt: openingBalanceDate,
                    createdBy: 'system',
                    createdByName: 'System',
                    source: 'payment', // Using 'payment' as the source type to match existing enum
                    patientName: ''
                };
                // Add opening balance as first ledger entry
                ledgerItems.push(openingBalanceEntry);
                this.logger.log('Added opening balance entry to ledger for new owner', {
                    ownerId,
                    openingBalance: openingBalanceAmount,
                    entryDate: openingBalanceDate
                });
            }
            else if (hasConsolidationInvoice) {
                this.logger.log('Skipping explicit opening balance entry due to consolidation invoice', {
                    ownerId,
                    openingBalance: openingBalanceAmount,
                    hasConsolidationInvoice
                });
            }
            // 6. Sort all ledger items chronologically for calculation
            ledgerItems.sort((a, b) => {
                const timeA = new Date(a.createdAt).getTime();
                const timeB = new Date(b.createdAt).getTime();
                if (timeA !== timeB) {
                    return timeA - timeB; // Ascending for calculation
                }
                // Secondary sort for items at the exact same millisecond to ensure consistency
                // Prefer 'Invoice' and 'Credit Note (Issued)' before 'Payment' if same time
                const typeOrderValue = (item) => {
                    if (item.type === 'Opening Balance')
                        return 0; // Always first
                    if (item.source === 'invoice')
                        return 1;
                    if (item.source === 'creditnote')
                        return 2;
                    if (item.source === 'payment') {
                        // For payments, "Add to Credits" might come before "Credit Note Refund (Cash)"
                        // if they are part of the same refund operation.
                        if (item.type === 'Add to Credits')
                            return 3;
                        if (item.type === 'Credit Note Refund (Cash)')
                            return 4;
                        return 5; // Other payments
                    }
                    return 6;
                };
                const orderA = typeOrderValue(a);
                const orderB = typeOrderValue(b);
                if (orderA !== orderB) {
                    return orderA - orderB;
                }
                return a.id.localeCompare(b.id); // Fallback to ID for stable sort
            });
            // 7. Calculate running balances and running credits
            let currentRunningBalance = 0; // Start at 0 instead of opening balance since we're adding it as an entry
            let currentRunningCredits = 0; // Start credits from 0 for the ledger period
            ledgerItems.forEach(item => {
                currentRunningBalance =
                    currentRunningBalance - item.debit + item.credit;
                item.runningBalance = currentRunningBalance;
                currentRunningCredits =
                    currentRunningCredits + item.creditChange;
                item.runningCredits = currentRunningCredits;
            });
            // 8. Sort for display (typically newest first)
            ledgerItems.sort((a, b) => {
                const timeA = new Date(a.createdAt).getTime();
                const timeB = new Date(b.createdAt).getTime();
                if (timeA !== timeB) {
                    return timeB - timeA; // Descending for display
                }
                // Secondary sort for items at the exact same millisecond (reversed from calculation sort)
                const typeOrderValue = (item) => {
                    if (item.source === 'invoice')
                        return 5; // Later in display if same time as payment
                    if (item.source === 'creditnote')
                        return 4;
                    if (item.source === 'payment') {
                        if (item.type === 'Credit Note Refund (Cash)')
                            return 1; // Show cash part of refund first
                        if (item.type === 'Add to Credits')
                            return 2; // Then credit part
                        return 3;
                    }
                    return 6;
                };
                const orderA = typeOrderValue(a);
                const orderB = typeOrderValue(b);
                if (orderA !== orderB) {
                    return orderA - orderB;
                }
                return b.id.localeCompare(a.id); // Fallback to ID for stable sort
            });
            // 9. Prepare owner details
            const ownerDetails = {
                id: owner.id,
                name: `${owner.firstName || ''} ${owner.lastName || ''}`.trim(),
                balance: currentRunningBalance, // Final monetary running balance from ledger calculation
                credits: Number(owner.ownerCredits) || 0, // Overall current credits from owner profile
                createdAt: owner.createdAt,
                openingBalance: Number(owner.openingBalance) || 0
            };
            // 10. Calculate summary data
            const totalMonetaryDebits = ledgerItems.reduce((sum, item) => sum + (item.debit || 0), 0);
            const totalMonetaryCredits = ledgerItems.reduce((sum, item) => sum + (item.credit || 0), 0);
            const totalProfileCreditsAdded = ledgerItems.reduce((sum, item) => sum + (item.creditChange > 0 ? item.creditChange : 0), 0);
            const totalProfileCreditsUsed = ledgerItems.reduce((sum, item) => sum + (item.creditChange < 0 ? -item.creditChange : 0), // sum of absolute values of used credits
            0);
            // 11. Return combined result
            return {
                ownerDetails,
                ledgerItems,
                uniqueUsers: [
                    ...(paymentDetailsResult.uniqueUsers || []),
                    ...(invoicesResult.uniqueUsers || []),
                    ...(creditnoteResult.uniqueUsers || [])
                ].filter((user, index, self) => index === self.findIndex(u => u.id === user.id) &&
                    user.name !== 'Staff Member'),
                summary: {
                    totalMonetaryDebits,
                    totalMonetaryCredits,
                    finalRunningBalance: currentRunningBalance,
                    totalProfileCreditsAdded,
                    totalProfileCreditsUsed,
                    finalRunningCredits: currentRunningCredits
                }
            };
        }
        catch (error) {
            this.logger.error('Error in getOwnerLedger', {
                error,
                ownerId
            });
            throw error;
        }
    }
    async getOwnerPendingInvoices(ownerId, userId, filters, brandId, clinicId) {
        try {
            // 1. Get owner details
            const owner = await this.ownerBrandRepository.findOne({
                where: { id: ownerId }
            });
            if (!owner) {
                throw new common_1.NotFoundException(`Owner not found with ID: ${ownerId}`);
            }
            // Add the computeOwnerBalance method first
            const ownerBalance = await this.computeOwnerBalance(owner.id);
            const ownerDetails = {
                id: owner.id,
                name: `${owner.firstName || ''} ${owner.lastName || ''}`.trim(),
                balance: ownerBalance, // Use computed balance instead of stored value
                credits: Number(owner.ownerCredits || 0)
            };
            // 2. Create a query builder for finding invoices
            const query = this.invoiceRepository
                .createQueryBuilder('invoice')
                .leftJoin('patients', 'patient', 'invoice.patientId = patient.id')
                .leftJoin('users', 'user', 'invoice.created_by = user.id')
                .where('invoice.ownerId = :ownerId', { ownerId })
                .andWhere('invoice.brandId = :brandId', { brandId })
                .andWhere('invoice.clinicId = :clinicId', { clinicId })
                .andWhere('invoice.status != :cancelledStatus', {
                cancelledStatus: enum_invoice_status_1.EnumInvoiceStatus.CANCELLED
            })
                .andWhere('invoice.invoiceType = :invoiceType', {
                invoiceType: enum_invoice_types_1.EnumInvoiceType.Invoice
            });
            // Handle status filtering - if a comma-separated string is provided, split and use IN operator
            if (filters.status) {
                if (filters.status.includes(',')) {
                    // Multiple statuses provided as comma-separated string
                    const statuses = filters.status
                        .split(',')
                        .map(status => status.trim());
                    query.andWhere('invoice.status IN (:...statuses)', {
                        statuses
                    });
                }
                else {
                    // Single status
                    query.andWhere('invoice.status = :status', {
                        status: filters.status
                    });
                }
            }
            else {
                // Default to only PENDING if no status is provided
                query.andWhere('invoice.status = :status', {
                    status: enum_invoice_status_1.EnumInvoiceStatus.PENDING
                });
            }
            // Apply filters
            if (filters.startDate) {
                query.andWhere('invoice.createdAt >= :startDate', {
                    startDate: new Date(filters.startDate)
                });
            }
            if (filters.endDate) {
                query.andWhere('invoice.createdAt <= :endDate', {
                    endDate: new Date(filters.endDate)
                });
            }
            // Filter by creator/user ID if provided
            if (filters.userId) {
                query.andWhere('invoice.created_by = :userId', {
                    userId: filters.userId
                });
            }
            // Apply search term if provided
            if (filters.searchTerm && filters.searchTerm.trim() !== '') {
                const searchTerm = `%${filters.searchTerm.toLowerCase()}%`;
                query.andWhere(`(
						LOWER(invoice.reference_alpha_id) LIKE LOWER(:searchTerm) OR
						LOWER(patient.patient_name) LIKE LOWER(:searchTerm) OR
						LOWER(invoice.status) LIKE LOWER(:searchTerm) OR
						LOWER(user.first_name) LIKE LOWER(:searchTerm) OR
						LOWER(user.last_name) LIKE LOWER(:searchTerm)
					)`, { searchTerm });
            }
            // Apply pet name filter if provided
            if (filters.petName && filters.petName.trim() !== '') {
                query.andWhere('LOWER(patient.patient_name) LIKE LOWER(:petName)', {
                    petName: `%${filters.petName.toLowerCase()}%`
                });
            }
            // Get all invoices, ordered by createdAt in descending order
            const pendingInvoices = await query
                .orderBy('invoice.createdAt', 'DESC')
                .getMany();
            if (pendingInvoices.length === 0) {
                return {
                    ownerDetails,
                    pendingInvoices: []
                };
            }
            // 3. Process invoices to get only required information
            const processedInvoices = await Promise.all(pendingInvoices.map(async (invoice) => {
                // Get patient name
                let patientName = 'Unknown';
                try {
                    if (invoice.patientId) {
                        const patient = await this.patientService.findOne(invoice.patientId);
                        if (patient && patient.patientName) {
                            patientName = patient.patientName;
                        }
                    }
                }
                catch (error) {
                    this.logger.error('Error fetching patient', {
                        error,
                        patientId: invoice.patientId
                    });
                }
                // Return only the required fields
                return {
                    id: invoice.id,
                    invoiceDate: invoice.createdAt,
                    metadata: invoice.metadata || {},
                    patientId: invoice.patientId,
                    patientName,
                    invoiceReference: invoice.referenceAlphaId ||
                        String(invoice.referenceId || ''),
                    balanceDue: Number(invoice.balanceDue || 0),
                    invoiceAmount: Number(invoice.invoiceAmount || 0),
                    totalCollected: Number(invoice.paidAmount || 0),
                    comment: 'Comment', // Added placeholder comment field
                    status: invoice.status // Include status in the response
                };
            }));
            return {
                ownerDetails,
                pendingInvoices: processedInvoices
            };
        }
        catch (error) {
            this.logger.error('Error in getOwnerPendingInvoices', {
                error,
                ownerId
            });
            throw error;
        }
    }
    async createBulkPaymentDetails(bulkPaymentDto, clinicId, brandId, userId) {
        const queryRunner = this.connection.createQueryRunner();
        const requestId = (0, uuidv7_1.uuidv4)();
        const logContext = {
            clinicId,
            brandId,
            userId,
            ownerId: bulkPaymentDto.ownerId,
            invoiceIds: bulkPaymentDto.invoiceIds,
            patientId: bulkPaymentDto.patientId,
            requestId
        };
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.logPaymentEvent(this.logger, logContext, 'Starting bulk payment processing', { bulkPaymentDto });
            // Fetch owner data
            const ownerData = await this.fetchOwnerData(queryRunner, bulkPaymentDto.ownerId, brandId);
            // Verify credit availability
            const { creditAmountToUse } = await this.verifyCreditAvailability(queryRunner, bulkPaymentDto, ownerData, logContext);
            // Calculate total payment corpus
            const totalPaymentCorpus = bulkPaymentDto.cashAmount + creditAmountToUse;
            this.logPaymentEvent(this.logger, logContext, 'Calculated payment corpus', {
                cashAmount: bulkPaymentDto.cashAmount,
                creditAmount: creditAmountToUse,
                totalPaymentCorpus
            });
            // Fetch invoices
            const invoices = await this.fetchInvoicesForBulkPayment(queryRunner, bulkPaymentDto, clinicId, brandId, logContext);
            // Generate unique reference IDs
            const cashReferenceAlphaId = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('referenceAlphaId', this.paymentDetailsRepository);
            const creditReferenceAlphaId = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('referenceAlphaId', this.paymentDetailsRepository);
            // Process payments
            let remainingCashAmount = bulkPaymentDto.cashAmount;
            let remainingCreditAmount = creditAmountToUse;
            let totalCashApplied = 0;
            let totalCreditApplied = 0;
            const createdPayments = [];
            for (const invoice of invoices) {
                const { cashApplied, creditApplied, payments } = await this.processInvoicePayment(queryRunner, invoice, bulkPaymentDto, remainingCashAmount, remainingCreditAmount, cashReferenceAlphaId, creditReferenceAlphaId, clinicId, brandId, userId, logContext);
                remainingCashAmount -= cashApplied;
                remainingCreditAmount -= creditApplied;
                totalCashApplied += cashApplied;
                totalCreditApplied += creditApplied;
                createdPayments.push(...payments);
            }
            // Handle excess cash as credits
            if (remainingCashAmount > 0) {
                const excessPayment = await this.handleExcessCashAsCredits(queryRunner, bulkPaymentDto, remainingCashAmount, cashReferenceAlphaId, clinicId, brandId, userId, logContext);
                createdPayments.push(excessPayment);
            }
            // Update owner credits
            const newCredits = Number(ownerData.ownerCredits || 0) -
                totalCreditApplied +
                remainingCashAmount;
            ownerData.ownerCredits = newCredits;
            await queryRunner.manager.save(ownerData);
            this.logPaymentEvent(this.logger, logContext, 'Updated owner credits after bulk payment', {
                previousCredits: Number(ownerData.ownerCredits || 0),
                newCredits,
                totalCashApplied,
                totalCreditApplied,
                excessToCredits: remainingCashAmount
            });
            await queryRunner.commitTransaction();
            return {
                payments: createdPayments,
                totalCashApplied,
                totalCreditApplied,
                amountAddedToCredits: remainingCashAmount,
                message: `Successfully processed payment for ${invoices.length} invoices`
            };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logPaymentEvent(this.logger, logContext, 'Error in bulk payment processing', { error }, 'error');
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async fetchInvoicesForBulkPayment(queryRunner, bulkPaymentDto, clinicId, brandId, logContext) {
        let invoices = [];
        const whereClause = !bulkPaymentDto.invoiceIds || bulkPaymentDto.invoiceIds.length === 0
            ? {
                ownerId: bulkPaymentDto.ownerId,
                status: (0, typeorm_2.In)([
                    enum_invoice_status_1.EnumInvoiceStatus.PENDING,
                    enum_invoice_status_1.EnumInvoiceStatus.PARTIALLY_PAID
                ]),
                brandId,
                clinicId,
                invoiceType: enum_invoice_types_1.EnumInvoiceType.Invoice
            }
            : {
                id: (0, typeorm_2.In)(bulkPaymentDto.invoiceIds || []),
                ownerId: bulkPaymentDto.ownerId
            };
        if (!bulkPaymentDto.invoiceIds ||
            bulkPaymentDto.invoiceIds.length === 0) {
            this.logPaymentEvent(this.logger, logContext, 'No specific invoices selected, fetching all pending invoices', { ownerId: bulkPaymentDto.ownerId });
        }
        invoices = await queryRunner.manager.find(invoice_entity_1.InvoiceEntity, {
            where: whereClause,
            order: { createdAt: 'ASC' }
        });
        if (invoices.length === 0) {
            this.logPaymentEvent(this.logger, logContext, 'No valid invoices found for bulk payment', {
                requestedInvoiceIds: bulkPaymentDto.invoiceIds || 'All pending invoices'
            }, 'error');
            throw new Error('No valid invoices found for the requested owner');
        }
        return invoices;
    }
    async processInvoicePayment(queryRunner, invoice, bulkPaymentDto, remainingCashAmount, remainingCreditAmount, cashReferenceAlphaId, creditReferenceAlphaId, clinicId, brandId, userId, logContext) {
        const pendingAmount = Number(invoice.invoiceAmount) - Number(invoice.paidAmount || 0);
        if (pendingAmount <= 0) {
            this.logPaymentEvent(this.logger, logContext, 'Skipping already paid invoice', { invoiceId: invoice.id, status: invoice.status });
            return { cashApplied: 0, creditApplied: 0, payments: [] };
        }
        const cashApplied = Math.min(remainingCashAmount, pendingAmount);
        const remainingAfterCash = pendingAmount - cashApplied;
        const creditApplied = remainingAfterCash > 0 && remainingCreditAmount > 0
            ? Math.min(remainingCreditAmount, remainingAfterCash)
            : 0;
        const payments = [];
        if (cashApplied > 0) {
            const cashPayment = await this.createPaymentEntityForBulk(queryRunner, bulkPaymentDto, invoice, cashApplied, 0, cashReferenceAlphaId, clinicId, brandId, userId, 'cash');
            payments.push(cashPayment);
        }
        if (creditApplied > 0) {
            const creditPayment = await this.createPaymentEntityForBulk(queryRunner, bulkPaymentDto, invoice, 0, creditApplied, creditReferenceAlphaId, clinicId, brandId, userId, 'credit');
            payments.push(creditPayment);
            await this.createCreditTransaction(queryRunner, bulkPaymentDto.ownerId, creditApplied, enum_credit_transaction_type_1.CreditTransactionType.USE, `Credits used for invoice payment`, { paymentId: creditPayment.id, invoiceId: invoice.id }, clinicId, brandId, userId);
        }
        const newPaidAmount = Number(invoice.paidAmount || 0) + cashApplied + creditApplied;
        const newBalanceDue = Math.max(0, Number(invoice.invoiceAmount) - newPaidAmount);
        const newStatus = newBalanceDue <= 0
            ? enum_invoice_status_1.EnumInvoiceStatus.FULLY_PAID
            : newPaidAmount > 0
                ? enum_invoice_status_1.EnumInvoiceStatus.PARTIALLY_PAID
                : invoice.status;
        await queryRunner.manager.update(invoice_entity_1.InvoiceEntity, { id: invoice.id }, {
            status: newStatus,
            paidAmount: newPaidAmount,
            balanceDue: newBalanceDue,
            updatedAt: new Date()
        });
        this.logPaymentEvent(this.logger, logContext, 'Updated invoice after payment', {
            invoiceId: invoice.id,
            newStatus,
            newPaidAmount,
            cashApplied,
            creditsApplied: creditApplied
        });
        return { cashApplied, creditApplied, payments };
    }
    async createPaymentEntityForBulk(queryRunner, bulkPaymentDto, invoice, cashAmount, creditAmount, referenceAlphaId, clinicId, brandId, userId, paymentType) {
        const payment = new payment_details_entity_1.PaymentDetailsEntity();
        payment.ownerId = bulkPaymentDto.ownerId;
        payment.invoiceId = invoice.id;
        // Use the patient ID from the invoice if available, otherwise fallback to the one from the DTO
        if (invoice.patientId) {
            payment.patientId = invoice.patientId;
        }
        else if (bulkPaymentDto.patientId) {
            payment.patientId = bulkPaymentDto.patientId;
        }
        // If there's only one invoice in the bulk payment, use ReconcileInvoice instead of BulkReconcileInvoice
        const isSingleInvoiceReconciliation = bulkPaymentDto.invoiceIds && bulkPaymentDto.invoiceIds.length === 1;
        payment.type = isSingleInvoiceReconciliation
            ? enum_credit_types_1.EnumAmountType.ReconcileInvoice
            : enum_credit_types_1.EnumAmountType.BulkReconcileInvoice;
        payment.amount = cashAmount;
        payment.paymentType =
            paymentType === 'credit'
                ? enum_payment_types_1.EnumPaymentType.Credits
                : (bulkPaymentDto.paymentType ||
                    enum_payment_types_1.EnumPaymentType.Cash);
        payment.transactionAmount = cashAmount;
        payment.amountPayable = cashAmount;
        payment.isCreditUsed = paymentType === 'credit';
        payment.creditAmountUsed = creditAmount;
        payment.isCreditsAdded = false;
        payment.creditAmountAdded = 0;
        payment.paymentNotes = bulkPaymentDto.description;
        payment.showInInvoice = true;
        payment.showInLedger = true;
        payment.brandId = brandId;
        payment.clinicId = clinicId;
        payment.previousBalance = 0; // No balance tracking
        payment.mainBalance = 0; // No balance tracking
        payment.createdBy = userId;
        payment.updatedBy = userId;
        payment.referenceAlphaId = referenceAlphaId;
        payment.receiptDetail = {};
        return await queryRunner.manager.save(payment);
    }
    async handleExcessCashAsCredits(queryRunner, bulkPaymentDto, remainingCashAmount, cashReferenceAlphaId, clinicId, brandId, userId, logContext) {
        const excessPayment = new payment_details_entity_1.PaymentDetailsEntity();
        excessPayment.ownerId = bulkPaymentDto.ownerId;
        excessPayment.type = enum_credit_types_1.EnumAmountType.Collect;
        excessPayment.amount = remainingCashAmount;
        excessPayment.paymentType = (bulkPaymentDto.paymentType ||
            'Cash');
        excessPayment.transactionAmount = 0;
        excessPayment.amountPayable = 0;
        excessPayment.isCreditUsed = false;
        excessPayment.creditAmountUsed = 0;
        excessPayment.isCreditsAdded = true;
        excessPayment.creditAmountAdded = remainingCashAmount;
        excessPayment.paymentNotes = bulkPaymentDto.description;
        excessPayment.showInInvoice = true;
        excessPayment.showInLedger = true;
        excessPayment.brandId = brandId;
        excessPayment.clinicId = clinicId;
        excessPayment.previousBalance = 0; // No balance tracking
        excessPayment.mainBalance = 0; // No balance tracking
        excessPayment.createdBy = userId;
        excessPayment.updatedBy = userId;
        excessPayment.referenceAlphaId = cashReferenceAlphaId;
        excessPayment.receiptDetail = {};
        const savedExcessPayment = await queryRunner.manager.save(excessPayment);
        await this.createCreditTransaction(queryRunner, bulkPaymentDto.ownerId, remainingCashAmount, enum_credit_transaction_type_1.CreditTransactionType.ADD, `Excess payment added as credits from bulk payment`, { paymentId: savedExcessPayment.id, invoiceId: null }, clinicId, brandId, userId);
        this.logPaymentEvent(this.logger, logContext, 'Handled excess cash as credits', {
            excessAmount: remainingCashAmount,
            paymentId: savedExcessPayment.id
        });
        return savedExcessPayment;
    }
    // Add the computeOwnerBalance method first
    /**
     * Fetch audit logs for multiple invoices efficiently
     * @param invoiceIds Array of invoice IDs to fetch audit logs for
     * @returns Map of invoice ID to audit logs
     */
    async fetchInvoiceAuditLogs(invoiceIds) {
        try {
            if (invoiceIds.length === 0) {
                return new Map();
            }
            // Fetch audit logs for all invoices in a single query
            const auditLogs = await this.invoiceAuditLogRepository
                .createQueryBuilder('auditLog')
                .leftJoinAndSelect('auditLog.user', 'user')
                .where('auditLog.invoiceId IN (:...invoiceIds)', {
                invoiceIds
            })
                .orderBy('auditLog.timestamp', 'DESC')
                .getMany();
            // Group audit logs by invoice ID
            const auditLogMap = new Map();
            auditLogs.forEach(auditLog => {
                const auditLogInfo = {
                    id: auditLog.id,
                    operationType: auditLog.operationType,
                    operations: auditLog.changedFieldsSummary, // Updated field name
                    timestamp: auditLog.timestamp,
                    userId: auditLog.userId,
                    userName: auditLog.userId === null
                        ? 'SYSTEM'
                        : auditLog.user
                            ? `${auditLog.user.firstName || ''} ${auditLog.user.lastName || ''}`.trim() ||
                                'Staff Member'
                            : 'Unknown User'
                };
                const existingLogs = auditLogMap.get(auditLog.invoiceId) || [];
                existingLogs.push(auditLogInfo);
                auditLogMap.set(auditLog.invoiceId, existingLogs);
            });
            return auditLogMap;
        }
        catch (error) {
            this.logger.error('Error fetching invoice audit logs', {
                error,
                invoiceIds
            });
            // Return empty map on error to not break the main functionality
            return new Map();
        }
    }
    async computeOwnerBalance(ownerId) {
        try {
            // Find all invoices associated with the owner that have pending or partially paid balances
            const invoices = await this.invoiceRepository.find({
                where: [
                    {
                        ownerId,
                        status: enum_invoice_status_1.EnumInvoiceStatus.PENDING,
                        balanceDue: (0, typeorm_2.Not)(0)
                    },
                    {
                        ownerId,
                        status: enum_invoice_status_1.EnumInvoiceStatus.PARTIALLY_PAID,
                        balanceDue: (0, typeorm_2.Not)(0)
                    }
                ],
                select: ['id', 'balanceDue', 'status']
            });
            // Sum up the balance due from all invoices
            // Negate the sum since pending balance represents money owed by the owner (negative balance)
            const totalBalance = -1 *
                invoices.reduce((sum, invoice) => sum + Number(invoice.balanceDue), 0);
            return totalBalance;
        }
        catch (error) {
            this.logger.error('Error computing owner balance', {
                error,
                ownerId
            });
            throw new common_1.InternalServerErrorException('Failed to compute owner balance');
        }
    }
    /**
     * Handle payment document operations (share or download)
     * @param referenceAlphaId Reference alpha ID for the document
     * @param documentType Type of document (payment-details)
     * @param action Whether to share or download
     * @param shareMethod Method to use for sharing (if applicable)
     * @param brandId Brand ID
     * @param userId User ID
     * @returns Response with status and data
     */
    async handlePaymentDocument(referenceAlphaId, documentType, action, shareMethod, brandId, userId, recipient, email, phoneNumber) {
        this.loggerService.log('Payment service: handlePaymentDocument called', {
            referenceAlphaId,
            documentType,
            action,
            shareMethod,
            brandId,
            userId,
            recipient,
            email,
            phoneNumber
        });
        try {
            // Fetch all payment details entries with this referenceAlphaId and brandId
            const paymentDetails = await this.paymentDetailsRepository.find({
                where: {
                    referenceAlphaId,
                    brandId
                }
            });
            // Log the entries
            this.loggerService.log('Found payment details for document request', {
                count: paymentDetails.length,
                referenceAlphaId,
                brandId
            });
            // Get the first payment detail to extract common information
            const firstPayment = paymentDetails[0];
            if (!firstPayment) {
                throw new common_1.NotFoundException('No payment details found');
            }
            // Get patient and owner details for validation and file naming
            const patientId = firstPayment.patientId;
            let patientDetails = null;
            let ownerDetails = null;
            if (patientId) {
                try {
                    patientDetails =
                        await this.patientService.getPatientDetails(patientId);
                    if (firstPayment.ownerId) {
                        ownerDetails = await this.ownerBrandRepository.findOne({
                            where: {
                                id: firstPayment.ownerId,
                                brandId
                            }
                        });
                    }
                }
                catch (error) {
                    this.loggerService.error('Error fetching patient or owner details', {
                        error,
                        patientId,
                        ownerId: firstPayment.ownerId
                    });
                    // Continue without patient/owner details
                }
            }
            // Find permanent and temporary file keys across all payment details
            let existingPermanentFileKey;
            let permanentFileName;
            const temporaryFileKeys = [];
            // Check all payment details for file keys
            for (const payment of paymentDetails) {
                const receiptDetail = payment.receiptDetail;
                // Look for permanent file keys (starts with 'receipt/')
                if ((receiptDetail === null || receiptDetail === void 0 ? void 0 : receiptDetail.fileKey) &&
                    receiptDetail.fileKey.startsWith('receipt/') &&
                    !existingPermanentFileKey) {
                    existingPermanentFileKey = receiptDetail.fileKey;
                    permanentFileName = receiptDetail.fileName;
                }
                // Collect temporary file keys (starts with 'receipt_temp/')
                else if ((receiptDetail === null || receiptDetail === void 0 ? void 0 : receiptDetail.fileKey) &&
                    receiptDetail.fileKey.startsWith('receipt_temp/') &&
                    !temporaryFileKeys.includes(receiptDetail.fileKey)) {
                    temporaryFileKeys.push(receiptDetail.fileKey);
                }
            }
            const hasPermanentFile = !!existingPermanentFileKey;
            this.loggerService.log('Payment document file status', {
                referenceAlphaId,
                hasPermanentFile,
                existingPermanentFileKey,
                hasTemporaryFiles: temporaryFileKeys.length > 0,
                temporaryFileKeys
            });
            // If a permanent file exists, make sure all payment details have the same file info
            if (hasPermanentFile) {
                const receiptDetail = {
                    fileKey: existingPermanentFileKey,
                    fileName: permanentFileName ||
                        `payment_receipt_${referenceAlphaId}.pdf`,
                    fileType: 'PDF'
                };
                // Update all payment records that don't already have this permanent file key
                const updatePromises = paymentDetails
                    .filter(payment => {
                    const detail = payment.receiptDetail;
                    return (!(detail === null || detail === void 0 ? void 0 : detail.fileKey) ||
                        detail.fileKey !== existingPermanentFileKey);
                })
                    .map(payment => {
                    payment.receiptDetail = receiptDetail;
                    return this.paymentDetailsRepository.save(payment);
                });
                if (updatePromises.length > 0) {
                    await Promise.all(updatePromises);
                    this.loggerService.log('Synced permanent file key to all payment records', {
                        referenceAlphaId,
                        fileKey: existingPermanentFileKey,
                        updatedCount: updatePromises.length
                    });
                }
            }
            // If temporary files exist but no permanent file, clean them up
            if (temporaryFileKeys.length > 0 && !hasPermanentFile) {
                // Delete all temporary files from S3
                for (const tempFileKey of temporaryFileKeys) {
                    try {
                        await this.s3Service.deleteFile(tempFileKey);
                        this.loggerService.log('Deleted temporary receipt file', {
                            tempFileKey,
                            referenceAlphaId
                        });
                    }
                    catch (deleteError) {
                        // Just log the error but continue with the process
                        this.loggerService.error('Failed to delete temporary receipt file', {
                            error: deleteError,
                            tempFileKey,
                            referenceAlphaId
                        });
                    }
                }
                // Clear file keys from all payment records with temporary files and set isGenerating=true
                const clearPromises = paymentDetails
                    .filter(payment => {
                    const detail = payment.receiptDetail;
                    return ((detail === null || detail === void 0 ? void 0 : detail.fileKey) &&
                        detail.fileKey.startsWith('receipt_temp/'));
                })
                    .map(payment => {
                    // Instead of empty object, set isGenerating flag
                    payment.receiptDetail = { isGenerating: 'true' };
                    return this.paymentDetailsRepository.save(payment);
                });
                if (clearPromises.length > 0) {
                    await Promise.all(clearPromises);
                    this.loggerService.log('Cleared temporary file keys and set generation flag on payment records', {
                        referenceAlphaId,
                        clearedCount: clearPromises.length
                    });
                }
            }
            // For download requests with existing permanent file, return URL immediately
            if (action === 'download' && hasPermanentFile) {
                this.loggerService.log('Using existing permanent file for download', {
                    fileKey: existingPermanentFileKey,
                    referenceAlphaId
                });
                try {
                    // Generate a formatted filename
                    let fileName = permanentFileName ||
                        `payment_receipt_${referenceAlphaId}.pdf`;
                    // If we have patient/owner details, create a more descriptive filename
                    if (patientDetails && ownerDetails) {
                        const petName = patientDetails.patientName || 'pet';
                        const ownerName = ownerDetails.lastName || 'owner';
                        const formattedPetName = petName
                            .replace(/[^a-zA-Z0-9]/g, '_')
                            .toLowerCase();
                        const formattedOwnerName = ownerName
                            .replace(/[^a-zA-Z0-9]/g, '_')
                            .toLowerCase();
                        fileName = `${formattedPetName}_${formattedOwnerName}_receipt_${referenceAlphaId}.pdf`;
                    }
                    // Get download URL from S3
                    const downloadUrl = await this.s3Service.getDownloadPreSignedUrl(existingPermanentFileKey, fileName);
                    return {
                        status: 'success',
                        data: {
                            downloadUrl,
                            fileName
                        }
                    };
                }
                catch (error) {
                    this.loggerService.error('Error generating download URL for permanent file', {
                        error,
                        fileKey: existingPermanentFileKey,
                        referenceAlphaId
                    });
                    throw new common_1.InternalServerErrorException('Failed to generate download URL');
                }
            }
            // Queue the task for asynchronous processing
            await this.queuePaymentDocumentTask(referenceAlphaId, action, shareMethod, brandId, userId, existingPermanentFileKey, // Only pass the permanent file key if available
            recipient, email, phoneNumber);
            // Return immediate response with status
            return {
                status: 'processing',
                data: {
                    referenceAlphaId,
                    message: `Document ${action} request is being processed`
                }
            };
        }
        catch (error) {
            this.loggerService.error('Error in handlePaymentDocument', {
                error,
                referenceAlphaId,
                action,
                shareMethod,
                recipient,
                email,
                phoneNumber
            });
            // Pass through HttpExceptions
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            // For other errors, throw a generic internal server error
            throw new common_1.InternalServerErrorException('Failed to process document request');
        }
    }
    /**
     * Check if a payment receipt PDF has been generated and is ready
     * @param referenceAlphaId Reference alpha ID of the payment to check
     * @returns Status and URL if ready
     */
    async checkPaymentDocumentStatus(referenceAlphaId) {
        this.logger.log('Checking payment document status', {
            referenceAlphaId
        });
        try {
            // Find all payment details with this reference ID
            const paymentDetails = await this.paymentDetailsRepository.find({
                where: { referenceAlphaId }
            });
            if (!paymentDetails || paymentDetails.length === 0) {
                throw new common_1.NotFoundException('Payment details not found');
            }
            // First check if any record has the isGenerating flag set
            for (const payment of paymentDetails) {
                const receiptDetail = payment.receiptDetail;
                if ((receiptDetail === null || receiptDetail === void 0 ? void 0 : receiptDetail.isGenerating) === 'true') {
                    this.logger.log('Document generation in progress', {
                        referenceAlphaId
                    });
                    return {
                        status: true,
                        data: {
                            isReady: false
                        },
                        message: 'Document is still being generated'
                    };
                }
            }
            // Check if any of the payment details has a file key - either permanent or temporary
            let fileKey;
            let fileName;
            let isPermanent = false;
            // First priority: Check for permanent files (receipt/)
            for (const payment of paymentDetails) {
                const receiptDetail = payment.receiptDetail;
                if ((receiptDetail === null || receiptDetail === void 0 ? void 0 : receiptDetail.fileKey) &&
                    receiptDetail.fileKey.startsWith('receipt/')) {
                    fileKey = receiptDetail.fileKey;
                    fileName =
                        receiptDetail.fileName ||
                            `payment_receipt_${referenceAlphaId}.pdf`;
                    isPermanent = true;
                    break;
                }
            }
            // Second priority: Check for temporary files (receipt_temp/)
            if (!fileKey) {
                for (const payment of paymentDetails) {
                    const receiptDetail = payment.receiptDetail;
                    if ((receiptDetail === null || receiptDetail === void 0 ? void 0 : receiptDetail.fileKey) &&
                        receiptDetail.fileKey.startsWith('receipt_temp/')) {
                        fileKey = receiptDetail.fileKey;
                        fileName =
                            receiptDetail.fileName ||
                                `payment_receipt_${referenceAlphaId}.pdf`;
                        isPermanent = false;
                        break;
                    }
                }
            }
            if (!fileKey) {
                // No document has been generated yet
                return {
                    status: true,
                    data: {
                        isReady: false
                    },
                    message: 'Document is still being generated'
                };
            }
            // Document is ready, generate a download URL
            const url = await this.s3Service.getDownloadPreSignedUrl(fileKey, fileName || `payment_receipt_${referenceAlphaId}.pdf`);
            // Log additional info about the document found
            this.logger.log('Found payment document file', {
                referenceAlphaId,
                fileKey,
                isPermanent,
                fileName
            });
            return {
                status: true,
                data: {
                    isReady: true,
                    url,
                    fileName,
                    isPermanent
                },
                message: 'Document is ready for download'
            };
        }
        catch (error) {
            this.logger.error('Error checking payment document status', {
                error,
                referenceAlphaId
            });
            throw error;
        }
    }
    /**
     * Queue a task for payment document generation and/or sharing
     * @param referenceAlphaId Reference alpha ID of the payment
     * @param action Whether to share or download
     * @param shareMethod Method to use for sharing (if applicable)
     * @param brandId Brand ID
     * @param userId User ID
     * @param fileKey Optional file key to use for the document
     */
    async queuePaymentDocumentTask(referenceAlphaId, action, shareMethod, brandId, userId, fileKey, recipient, email, phoneNumber) {
        try {
            // Validate fileKey if provided
            if (fileKey &&
                fileKey.startsWith('receipt/') &&
                !fileKey.includes(referenceAlphaId)) {
                this.logger.warn('Permanent fileKey does not contain referenceAlphaId', {
                    referenceAlphaId,
                    fileKey
                });
            }
            // If no permanent file exists or we're not using it, set isGenerating flag on all records
            if (!fileKey || action === 'share') {
                // Find all payment details with this reference ID
                const payments = await this.paymentDetailsRepository.find({
                    where: { referenceAlphaId }
                });
                if (payments && payments.length > 0) {
                    // Update all records with the isGenerating flag
                    const updatePromises = payments.map(payment => {
                        const receiptDetail = payment.receiptDetail ||
                            {};
                        receiptDetail.isGenerating = 'true';
                        payment.receiptDetail = receiptDetail;
                        return this.paymentDetailsRepository.save(payment);
                    });
                    await Promise.all(updatePromises);
                    this.logger.log('Set generation flag on payment records', {
                        referenceAlphaId,
                        updatedCount: payments.length
                    });
                }
            }
            // Log detailed info about the request
            this.logger.log('Queueing payment document task', {
                referenceAlphaId,
                action,
                shareMethod,
                recipient,
                email,
                phoneNumber,
                fileKey,
                isPermanentFile: fileKey && fileKey.startsWith('receipt/'),
                hasFileKey: !!fileKey,
                fileKeyLength: (fileKey === null || fileKey === void 0 ? void 0 : fileKey.length) || 0
            });
            await this.sqsService.sendMessage({
                queueKey: 'NidanaInvoiceTasks',
                messageBody: {
                    data: {
                        taskType: 'processPaymentDocument',
                        referenceAlphaId,
                        action,
                        shareMethod,
                        brandId,
                        userId,
                        fileKey,
                        recipient,
                        email,
                        phoneNumber,
                        // Include a flag to clear the generation flag when complete
                        clearGeneratingFlag: true
                    }
                },
                deduplicationId: `payment-document-${referenceAlphaId}-${Date.now()}`
            });
            this.logger.log('Payment document task queued successfully', {
                referenceAlphaId,
                action,
                fileKey,
                recipient
            });
        }
        catch (error) {
            this.logger.error('Failed to queue payment document task', {
                error,
                referenceAlphaId,
                action,
                recipient
            });
            throw new common_1.HttpException('Failed to process document request', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    /**
     * Find payment details by reference alpha ID
     * @param referenceAlphaId The reference alpha ID to search for
     * @param brandId Optional brand ID to filter by
     * @returns Array of payment details matching the reference alpha ID
     */
    async findPaymentsByReferenceAlphaId(referenceAlphaId, brandId) {
        const query = {
            where: { referenceAlphaId }
        };
        if (brandId) {
            query.where.brandId = brandId;
        }
        return this.paymentDetailsRepository.find(query);
    }
    /**
     * Create payment details within an existing transaction
     * This method allows payment creation to be part of a larger atomic operation
     */
    async createPaymentDetailsWithTransaction(paymentDetailsDto, clinicId, brandId, userId, queryRunner) {
        const logContext = {
            clinicId,
            brandId,
            userId,
            ownerId: paymentDetailsDto.ownerId,
            invoiceId: paymentDetailsDto.invoiceId,
            patientId: paymentDetailsDto.patientId,
            requestId: (0, uuidv7_1.uuidv4)(),
            transactionMode: 'external'
        };
        try {
            this.logPaymentEvent(this.logger, logContext, 'Start creating payment detail within existing transaction', { paymentDetailsDto });
            // Fetch owner data using the external transaction
            const ownerData = await this.fetchOwnerDataWithTransaction(queryRunner, paymentDetailsDto.ownerId, brandId);
            // Validate invoice using the external transaction
            const invoice = paymentDetailsDto.invoiceId
                ? await this.validateInvoiceWithTransaction(queryRunner, paymentDetailsDto.invoiceId, paymentDetailsDto)
                : null;
            // Additional validation for atomic operations
            if (paymentDetailsDto.invoiceId && !invoice) {
                this.logPaymentEvent(this.logger, logContext, 'ERROR: Invoice not found in transaction', { invoiceId: paymentDetailsDto.invoiceId }, 'error');
                throw new Error(`Invoice with ID ${paymentDetailsDto.invoiceId} not found in current transaction`);
            }
            if (invoice) {
                this.logPaymentEvent(this.logger, logContext, 'Invoice validated successfully in transaction', {
                    invoiceId: invoice.id,
                    invoiceStatus: invoice.status,
                    invoiceAmount: invoice.invoiceAmount
                });
            }
            // Calculate credits and excess
            const originalCredits = Number(ownerData.ownerCredits || 0);
            this.logPaymentEvent(this.logger, logContext, 'Starting balance calculation in transaction', {
                originalAmount: paymentDetailsDto.amount,
                originalCredits,
                isCreditUsed: paymentDetailsDto.isCreditUsed,
                creditAmountRequested: paymentDetailsDto.creditAmountUsed,
                invoiceAmount: invoice === null || invoice === void 0 ? void 0 : invoice.invoiceAmount,
                invoicePaidAmount: invoice === null || invoice === void 0 ? void 0 : invoice.paidAmount,
                pendingAmount: invoice
                    ? Number(invoice.invoiceAmount) -
                        Number(invoice.paidAmount || 0)
                    : 0
            });
            const { newCredits, amountToApply, creditAmountUsed, excessAmount } = this.calculateNewBalances(paymentDetailsDto, ownerData, invoice);
            this.logPaymentEvent(this.logger, logContext, 'Balance calculation completed in transaction', {
                newCredits,
                amountToApply,
                creditAmountUsed,
                excessAmount,
                creditsChange: newCredits - originalCredits,
                totalPayment: amountToApply +
                    (paymentDetailsDto.isCreditUsed ? creditAmountUsed : 0)
            });
            const createdPayments = [];
            // Generate separate reference IDs for different payment types
            const cashReferenceAlphaId = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('referenceAlphaId', this.paymentDetailsRepository);
            // For credit notes, we may need separate reference IDs
            const isCreditNoteWithExcess = paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.CreditNote &&
                excessAmount > 0;
            // Create cash payment entity if amount > 0 or it's a special case
            if ((Number(paymentDetailsDto.amount) > 0 ||
                // Allow zero-amount payments for invoice types with transaction amount
                (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Invoice &&
                    Number(paymentDetailsDto.transactionAmount) > 0) ||
                // Allow zero-amount payments for Credit Notes
                paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.CreditNote) &&
                // We want to create a cash payment in all cases EXCEPT when:
                // 1. It's a credit-to-cash conversion (Return type with isCreditUsed=true)
                // 2. The amount to apply is zero and it's not an Invoice payment or Credit Note
                (amountToApply > 0 ||
                    paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Invoice ||
                    paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.CreditNote ||
                    // Special case for credit-to-cash conversion
                    (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Return &&
                        paymentDetailsDto.isCreditUsed))) {
                const cashPayment = await this.createCashPaymentEntityWithTransaction(queryRunner, paymentDetailsDto, paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Return &&
                    paymentDetailsDto.isCreditUsed
                    ? paymentDetailsDto.amount // For credit-to-cash, use full amount
                    : amountToApply, // For other cases, use amountToApply
                cashReferenceAlphaId, clinicId, brandId, userId);
                createdPayments.push(cashPayment);
                // Create credit transaction for Collect payment
                if (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Collect) {
                    await this.createCreditTransactionWithTransaction(queryRunner, ownerData.id, paymentDetailsDto.amount, enum_credit_transaction_type_1.CreditTransactionType.ADD, `Amount ${paymentDetailsDto.amount} added to credits via Collect payment`, {
                        paymentId: cashPayment.id,
                        invoiceId: null
                    }, clinicId, brandId, userId);
                }
            }
            // Create credit payment entity if credits are used
            if (paymentDetailsDto.isCreditUsed &&
                creditAmountUsed > 0 &&
                paymentDetailsDto.type !== enum_credit_types_1.EnumAmountType.Return // Don't create credit payment for credit-to-cash conversion
            ) {
                const creditReferenceAlphaId = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('referenceAlphaId', this.paymentDetailsRepository);
                const creditPayment = await this.createCreditPaymentEntityWithTransaction(queryRunner, paymentDetailsDto, creditAmountUsed, creditReferenceAlphaId, clinicId, brandId, userId);
                createdPayments.push(creditPayment);
                // Create credit transaction for credit usage
                await this.createCreditTransactionWithTransaction(queryRunner, ownerData.id, creditAmountUsed, enum_credit_transaction_type_1.CreditTransactionType.USE, `Credits used for ${paymentDetailsDto.invoiceId ? 'invoice ' + paymentDetailsDto.invoiceId : 'payment'}`, {
                    paymentId: creditPayment.id,
                    invoiceId: paymentDetailsDto.invoiceId
                }, clinicId, brandId, userId);
            }
            // For credit-to-cash conversion, create a credit transaction to track credit usage
            if (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.Return &&
                paymentDetailsDto.isCreditUsed &&
                creditAmountUsed > 0) {
                await this.createCreditTransactionWithTransaction(queryRunner, ownerData.id, creditAmountUsed, enum_credit_transaction_type_1.CreditTransactionType.USE, `Credits converted to cash return`, {
                    paymentId: createdPayments[0].id, // Reference the cash payment
                    invoiceId: null
                }, clinicId, brandId, userId);
            }
            // Update invoice status if applicable
            if (invoice) {
                await this.updateInvoiceStatusWithTransaction(queryRunner, invoice, paymentDetailsDto, amountToApply, creditAmountUsed);
            }
            // Handle excess amount from credit notes specially
            if (excessAmount > 0 &&
                paymentDetailsDto.type !== enum_credit_types_1.EnumAmountType.Collect) {
                // Generate a new reference ID for excess payment in credit notes
                const excessReferenceAlphaId = isCreditNoteWithExcess
                    ? await (0, generate_alpha_numeric_code_1.generateUniqueCode)('referenceAlphaId', this.paymentDetailsRepository)
                    : cashReferenceAlphaId;
                // Create the excess payment
                const excessPayment = await this.handleExcessAmountAsCreditsWithTransaction(queryRunner, paymentDetailsDto, excessAmount, excessReferenceAlphaId, clinicId, brandId, userId, logContext, 
                // Only for credit notes, include invoice ID and patient ID
                isCreditNoteWithExcess
                    ? {
                        includeInvoiceId: true,
                        includePatientId: true
                    }
                    : undefined);
                createdPayments.push(excessPayment);
            }
            // Update owner credits
            ownerData.ownerCredits = newCredits;
            await queryRunner.manager.save(ownerData);
            this.logPaymentEvent(this.logger, logContext, 'Payment processing completed within external transaction', { paymentIds: createdPayments.map(p => p.id), newCredits });
            return createdPayments;
        }
        catch (error) {
            this.logPaymentEvent(this.logger, logContext, 'Error in creating payment within external transaction', { errorMessage: error.message }, 'error');
            throw error;
        }
    }
    // Helper methods for transaction-aware operations
    async fetchOwnerDataWithTransaction(queryRunner, ownerId, brandId) {
        // Implementation similar to fetchOwnerData but using queryRunner
        // You'll need to implement this based on your existing fetchOwnerData method
        // For now, delegating to existing method which already accepts queryRunner
        return this.fetchOwnerData(queryRunner, ownerId, brandId);
    }
    async validateInvoiceWithTransaction(queryRunner, invoiceId, paymentDetailsDto) {
        // Implementation similar to validateInvoice but using queryRunner
        return this.validateInvoice(queryRunner, invoiceId, paymentDetailsDto);
    }
    async createCashPaymentEntityWithTransaction(queryRunner, paymentDetailsDto, cashAmount, referenceAlphaId, clinicId, brandId, userId) {
        // Implementation identical to createCashPaymentEntity but using queryRunner
        return this.createCashPaymentEntity(queryRunner, paymentDetailsDto, cashAmount, referenceAlphaId, clinicId, brandId, userId);
    }
    async createCreditPaymentEntityWithTransaction(queryRunner, paymentDetailsDto, creditAmount, referenceAlphaId, clinicId, brandId, userId) {
        // Implementation identical to createCreditPaymentEntity but using queryRunner
        return this.createCreditPaymentEntity(queryRunner, paymentDetailsDto, creditAmount, referenceAlphaId, clinicId, brandId, userId);
    }
    async createCreditTransactionWithTransaction(queryRunner, ownerId, amount, transactionType, description, relatedIds, clinicId, brandId, userId) {
        // Implementation identical to createCreditTransaction but using queryRunner
        return this.createCreditTransaction(queryRunner, ownerId, amount, transactionType, description, relatedIds, clinicId, brandId, userId);
    }
    async updateInvoiceStatusWithTransaction(queryRunner, invoice, paymentDetailsDto, amountToApply, creditAmountUsed) {
        // Calculate total payment amount including cash and credits
        const totalPayment = Number(amountToApply) + Number(creditAmountUsed);
        const newPaidAmount = Number(invoice.paidAmount || 0) + totalPayment;
        const newBalanceDue = Math.max(0, Number(invoice.invoiceAmount) - newPaidAmount);
        // Determine status based on balance
        const newStatus = newBalanceDue <= 0.01 // Allow for small rounding differences
            ? enum_invoice_status_1.EnumInvoiceStatus.FULLY_PAID
            : newPaidAmount > 0
                ? enum_invoice_status_1.EnumInvoiceStatus.PARTIALLY_PAID
                : invoice.status;
        await queryRunner.manager.update(invoice_entity_1.InvoiceEntity, { id: invoice.id }, {
            status: newStatus,
            paidAmount: newPaidAmount,
            balanceDue: newBalanceDue,
            updatedAt: new Date()
        });
        // Special handling for credit notes and refunds
        if (paymentDetailsDto.type === enum_credit_types_1.EnumAmountType.CreditNote &&
            invoice.invoiceType === enum_invoice_types_1.EnumInvoiceType.Refund) {
            await queryRunner.manager.update(invoice_entity_1.InvoiceEntity, { id: invoice.id }, {
                status: enum_invoice_status_1.EnumInvoiceStatus.FULLY_PAID,
                balanceDue: 0,
                updatedAt: new Date()
            });
        }
    }
    async handleExcessAmountAsCreditsWithTransaction(queryRunner, paymentDetailsDto, excessAmount, referenceAlphaId, clinicId, brandId, userId, logContext, options) {
        // Implementation identical to handleExcessAmountAsCredits but using queryRunner
        // Delegate to existing method
        return this.handleExcessAmountAsCredits(queryRunner, paymentDetailsDto, excessAmount, referenceAlphaId, clinicId, brandId, userId, logContext, options);
    }
    /**
     * Log changes to a payment detail for audit purposes
     * @param paymentDetailId ID of the payment detail
     * @param userId ID of the user making the change
     * @param operationType Type of operation (UPDATE or DELETE)
     * @param changes Before and after values for updates, null for deletes
     * @param operations Structured operations describing the change
     * @param queryRunner Optional query runner for transaction support
     * @param reason Optional reason for the change
     */
    async logChange(paymentDetailId, userId, operationType, changes, operations, queryRunner, reason) {
        try {
            // Use transaction manager if queryRunner is provided, otherwise use regular repository
            if (queryRunner) {
                const auditLog = queryRunner.manager.create(payment_details_audit_log_entity_1.PaymentDetailsAuditLogEntity, {
                    paymentDetailId,
                    userId,
                    operationType,
                    changes,
                    changedFieldsSummary: operations
                });
                await queryRunner.manager.save(payment_details_audit_log_entity_1.PaymentDetailsAuditLogEntity, auditLog);
            }
            else {
                const auditLog = this.paymentDetailsAuditLogRepository.create({
                    paymentDetailId,
                    userId,
                    operationType,
                    changes,
                    changedFieldsSummary: operations
                });
                await this.paymentDetailsAuditLogRepository.save(auditLog);
            }
            this.loggerService.log('Payment details audit log created', {
                paymentDetailId,
                userId,
                operationType,
                operationsCount: (operations === null || operations === void 0 ? void 0 : operations.length) || 0,
                usingTransaction: !!queryRunner,
                reason: reason || 'No reason provided'
            });
        }
        catch (error) {
            this.loggerService.error('Failed to create payment details audit log', {
                error,
                paymentDetailId,
                userId,
                operationType,
                usingTransaction: !!queryRunner
            });
            // Don't throw error to avoid breaking the main operation
        }
    }
    /**
     * Edit payment details (only payment mode can be edited)
     * @param paymentDetailId ID of the payment detail to edit
     * @param editDto Edit details including new payment type and comment
     * @param userId ID of the user making the edit
     * @returns Updated payment detail entity
     */
    async editPaymentDetails(paymentDetailId, editDto, userId) {
        var _a;
        const queryRunner = this.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.loggerService.log('🎯 Starting payment edit process', {
                paymentDetailId,
                newPaymentType: editDto.paymentType,
                userId,
                comment: editDto.comment
            });
            // 1. Find the payment detail
            const paymentDetail = await queryRunner.manager.findOne(payment_details_entity_1.PaymentDetailsEntity, {
                where: { id: paymentDetailId },
                relations: ['invoice']
            });
            if (!paymentDetail) {
                this.loggerService.error('❌ Payment detail not found', { paymentDetailId });
                throw new common_1.NotFoundException('Payment detail not found');
            }
            this.loggerService.log('✅ Found payment detail to edit', {
                paymentId: paymentDetail.id,
                currentPaymentType: paymentDetail.paymentType,
                referenceAlphaId: paymentDetail.referenceAlphaId,
                invoiceId: paymentDetail.invoiceId
            });
            // 2. Find all related payment details with the same reference_alpha_id
            this.loggerService.log('🔍 Searching for related payments to update', {
                referenceAlphaId: paymentDetail.referenceAlphaId
            });
            const relatedPayments = await queryRunner.manager.find(payment_details_entity_1.PaymentDetailsEntity, {
                where: {
                    referenceAlphaId: paymentDetail.referenceAlphaId,
                    deletedAt: (0, typeorm_2.IsNull)() // Only get non-deleted payments
                },
                relations: ['invoice']
            });
            this.loggerService.log('🔗 Found related payments to update', {
                referenceAlphaId: paymentDetail.referenceAlphaId,
                totalRelatedPayments: relatedPayments.length,
                paymentDetails: relatedPayments.map(p => ({
                    id: p.id,
                    type: p.type,
                    currentPaymentType: p.paymentType,
                    invoiceId: p.invoiceId
                }))
            });
            // 3. Update all related payment details
            for (const relatedPayment of relatedPayments) {
                this.loggerService.log('🔄 Processing payment update', {
                    paymentId: relatedPayment.id,
                    type: relatedPayment.type,
                    oldPaymentType: relatedPayment.paymentType,
                    newPaymentType: editDto.paymentType
                });
                // Store the before state for audit logging
                const beforeState = {
                    paymentType: relatedPayment.paymentType
                };
                // Store the after state for audit logging
                const afterState = {
                    paymentType: editDto.paymentType
                };
                // Update the payment detail
                await queryRunner.manager.update(payment_details_entity_1.PaymentDetailsEntity, { id: relatedPayment.id }, {
                    paymentType: editDto.paymentType,
                    updatedBy: userId,
                    updatedAt: new Date()
                });
                // Update the associated invoice's payment mode if it exists
                if (relatedPayment.invoiceId) {
                    this.loggerService.log('📋 Updating related invoice payment mode', {
                        invoiceId: relatedPayment.invoiceId,
                        newPaymentMode: editDto.paymentType
                    });
                    await queryRunner.manager.update(invoice_entity_1.InvoiceEntity, { id: relatedPayment.invoiceId }, {
                        paymentMode: editDto.paymentType,
                        updatedBy: userId,
                        updatedAt: new Date()
                    });
                }
                // Log the change in audit trail for each payment
                const operations = [
                    {
                        type: 'CHANGE_PAYMENT_MODE',
                        before: beforeState.paymentType,
                        after: afterState.paymentType,
                        description: relatedPayment.id === paymentDetailId
                            ? `Payment mode changed from ${beforeState.paymentType} to ${afterState.paymentType} (main)`
                            : `Payment mode changed from ${beforeState.paymentType} to ${afterState.paymentType} (related)`,
                        reason: editDto.comment
                    }
                ];
                this.loggerService.log('📝 Creating audit log for payment', {
                    paymentId: relatedPayment.id,
                    isMainPayment: relatedPayment.id === paymentDetailId,
                    operationType: 'UPDATE'
                });
                await this.logChange(relatedPayment.id, userId, payment_details_audit_log_entity_1.PaymentDetailsAuditLogOperationType.UPDATE, { before: beforeState, after: afterState }, operations, queryRunner, editDto.comment);
                this.loggerService.log('✅ Payment updated successfully', {
                    paymentId: relatedPayment.id,
                    type: relatedPayment.type,
                    oldPaymentType: beforeState.paymentType,
                    newPaymentType: afterState.paymentType
                });
            }
            // 4. Get the updated payment detail
            const updatedPaymentDetail = await queryRunner.manager.findOne(payment_details_entity_1.PaymentDetailsEntity, {
                where: { id: paymentDetailId },
                relations: ['invoice']
            });
            this.loggerService.log('💾 Committing payment edit transaction', {
                referenceAlphaId: paymentDetail.referenceAlphaId,
                totalPaymentsUpdated: relatedPayments.length,
                newPaymentType: editDto.paymentType
            });
            await queryRunner.commitTransaction();
            this.loggerService.log('🎉 Payment edit completed successfully', {
                referenceAlphaId: paymentDetail.referenceAlphaId,
                originalPaymentId: paymentDetailId,
                totalPaymentsUpdated: relatedPayments.length,
                updatedPaymentDetails: relatedPayments.map(p => ({
                    id: p.id,
                    type: p.type,
                    oldPaymentType: p.paymentType,
                    newPaymentType: editDto.paymentType
                })),
                auditInfo: {
                    userId,
                    comment: editDto.comment,
                    timestamp: new Date().toISOString()
                }
            });
            return updatedPaymentDetail;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            const errorType = ((_a = error === null || error === void 0 ? void 0 : error.constructor) === null || _a === void 0 ? void 0 : _a.name) || 'Unknown';
            this.loggerService.error('❌ Error occurred during payment edit - rolling back transaction', {
                paymentDetailId,
                error: errorMessage,
                stack: errorStack,
                errorType,
                userId,
                editDto
            });
            await queryRunner.rollbackTransaction();
            this.loggerService.error('🔄 Transaction rolled back due to error', {
                paymentDetailId,
                errorMessage
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to edit payment detail', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
        finally {
            this.loggerService.log('🔚 Releasing database connection', {
                paymentDetailId
            });
            await queryRunner.release();
        }
    }
    /**
     * Validate credit balance before allowing payment deletion
     * This handles the three edge cases for credit deletion validation
     */
    async validateCreditBalanceForDeletion(queryRunner, relatedPayments, ownerId) {
        this.loggerService.log('🔍 Starting credit balance validation for deletion', {
            ownerId,
            totalRelatedPayments: relatedPayments.length,
            paymentsToAnalyze: relatedPayments.map(p => ({
                id: p.id,
                type: p.type,
                isCreditUsed: p.isCreditUsed,
                creditAmountUsed: p.creditAmountUsed,
                isCreditsAdded: p.isCreditsAdded,
                creditAmountAdded: p.creditAmountAdded
            }))
        });
        // Get current owner credit balance
        const owner = await queryRunner.manager.findOne(owner_brand_entity_1.OwnerBrand, {
            where: { id: ownerId }
        });
        if (!owner) {
            throw new common_1.NotFoundException('Owner not found');
        }
        const currentCreditBalance = Number(owner.ownerCredits || 0);
        this.loggerService.log('📊 Current owner credit balance', {
            ownerId,
            currentCreditBalance
        });
        // Calculate the total impact of deletion on credit balance
        let totalCreditImpact = 0;
        const impactBreakdown = [];
        for (const payment of relatedPayments) {
            const creditAmountUsed = Number(payment.creditAmountUsed || 0);
            const creditAmountAdded = Number(payment.creditAmountAdded || 0);
            // Calculate impact for this payment
            let paymentImpact = 0;
            // If credits were used, deletion will restore them (positive impact)
            if (payment.isCreditUsed && creditAmountUsed > 0) {
                paymentImpact += creditAmountUsed;
                this.loggerService.log('💳 Credit usage will be restored', {
                    paymentId: payment.id,
                    creditAmountUsed,
                    impact: `+${creditAmountUsed}`
                });
            }
            // If credits were added, deletion will remove them (negative impact)
            if (payment.isCreditsAdded && creditAmountAdded > 0) {
                paymentImpact -= creditAmountAdded;
                this.loggerService.log('💰 Credit addition will be removed', {
                    paymentId: payment.id,
                    creditAmountAdded,
                    impact: `-${creditAmountAdded}`
                });
            }
            totalCreditImpact += paymentImpact;
            impactBreakdown.push({
                paymentId: payment.id,
                type: payment.type,
                creditAmountUsed,
                creditAmountAdded,
                paymentImpact,
                runningTotal: totalCreditImpact
            });
        }
        this.loggerService.log('📈 Total credit impact calculation', {
            currentCreditBalance,
            totalCreditImpact,
            projectedBalance: currentCreditBalance + totalCreditImpact,
            impactBreakdown
        });
        // Calculate the projected balance after deletion
        const projectedBalance = currentCreditBalance + totalCreditImpact;
        // Edge Case Validation: Don't allow deletion if it would result in negative credits
        if (projectedBalance < 0) {
            const shortfall = Math.abs(projectedBalance);
            this.loggerService.error('❌ Credit deletion validation failed - would result in negative balance', {
                ownerId,
                currentCreditBalance,
                totalCreditImpact,
                projectedBalance,
                shortfall,
                validationRule: 'Prevent negative credit balance',
                edgeCases: {
                    directCreditDeletion: 'Cannot delete credit addition when insufficient remaining credits',
                    overpaymentInvoiceDeletion: 'Cannot delete invoice payment when credit portion exceeds available credits',
                    preExistingCredits: 'Cannot remove added credits when current balance is lower than removal amount'
                }
            });
            throw new common_1.HttpException(`Cannot delete this payment. Deletion would result in a negative credit balance. ` +
                `Current credits: ₹${currentCreditBalance.toFixed(2)}, ` +
                `Impact of deletion: ₹${totalCreditImpact.toFixed(2)}, ` +
                `Resulting balance would be: ₹${projectedBalance.toFixed(2)}. ` +
                `You need at least ₹${shortfall.toFixed(2)} more in credits to allow this deletion.`, common_1.HttpStatus.BAD_REQUEST);
        }
        this.loggerService.log('✅ Credit balance validation passed', {
            ownerId,
            currentCreditBalance,
            totalCreditImpact,
            projectedBalance,
            validationStatus: 'PASSED - Deletion allowed'
        });
    }
    /**
     * Delete payment details with business validations
     * @param paymentDetailId ID of the payment detail to delete
     * @param deleteDto Delete details including comment
     * @param userId ID of the user making the deletion
     * @returns Success message
     */
    async deletePaymentDetails(paymentDetailId, deleteDto, userId) {
        var _a;
        const queryRunner = this.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.loggerService.log('🚀 Starting payment deletion process', {
                paymentDetailId,
                userId,
                comment: deleteDto.comment
            });
            // 1. Find the payment detail with all necessary relations
            const paymentDetail = await queryRunner.manager.findOne(payment_details_entity_1.PaymentDetailsEntity, {
                where: { id: paymentDetailId },
                relations: ['invoice']
            });
            if (!paymentDetail) {
                this.loggerService.error('❌ Payment detail not found', { paymentDetailId });
                throw new common_1.NotFoundException('Payment detail not found');
            }
            this.loggerService.log('✅ Found payment detail to delete', {
                paymentId: paymentDetail.id,
                type: paymentDetail.type,
                amount: paymentDetail.amount,
                referenceAlphaId: paymentDetail.referenceAlphaId,
                ownerId: paymentDetail.ownerId,
                invoiceId: paymentDetail.invoiceId,
                isCreditUsed: paymentDetail.isCreditUsed,
                creditAmountUsed: paymentDetail.creditAmountUsed,
                isCreditsAdded: paymentDetail.isCreditsAdded,
                creditAmountAdded: paymentDetail.creditAmountAdded,
                createdAt: paymentDetail.createdAt
            });
            // 2. Business validations
            // Check if it's a refund receipt (cannot be deleted)
            if (paymentDetail.type === enum_credit_types_1.EnumAmountType.Return ||
                paymentDetail.type === enum_credit_types_1.EnumAmountType.CreditNote) {
                this.loggerService.error('❌ Cannot delete refund receipt', {
                    paymentId: paymentDetail.id,
                    type: paymentDetail.type
                });
                throw new common_1.HttpException('Refund receipts cannot be deleted', common_1.HttpStatus.BAD_REQUEST);
            }
            // Check 12-hour time limit
            const createdAt = new Date(paymentDetail.createdAt);
            const now = new Date();
            const hoursDifference = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
            this.loggerService.log('⏰ Checking time limit', {
                createdAt: createdAt.toISOString(),
                now: now.toISOString(),
                hoursDifference: hoursDifference.toFixed(2),
                timeLimit: '12 hours'
            });
            if (hoursDifference > 12) {
                this.loggerService.error('❌ Payment too old to delete', {
                    paymentId: paymentDetail.id,
                    hoursDifference: hoursDifference.toFixed(2)
                });
                throw new common_1.HttpException('Payment details can only be deleted within 12 hours of creation', common_1.HttpStatus.BAD_REQUEST);
            }
            // 3. Find all related payment details with the same reference_alpha_id
            this.loggerService.log('🔍 Searching for related payments', {
                referenceAlphaId: paymentDetail.referenceAlphaId
            });
            const relatedPayments = await queryRunner.manager.find(payment_details_entity_1.PaymentDetailsEntity, {
                where: {
                    referenceAlphaId: paymentDetail.referenceAlphaId,
                    deletedAt: (0, typeorm_2.IsNull)() // Only get non-deleted payments
                },
                relations: ['invoice']
            });
            this.loggerService.log('🔗 Found related payments for deletion', {
                referenceAlphaId: paymentDetail.referenceAlphaId,
                totalRelatedPayments: relatedPayments.length,
                paymentDetails: relatedPayments.map(p => ({
                    id: p.id,
                    type: p.type,
                    amount: p.amount,
                    invoiceId: p.invoiceId,
                    isCreditUsed: p.isCreditUsed,
                    creditAmountUsed: p.creditAmountUsed,
                    isCreditsAdded: p.isCreditsAdded,
                    creditAmountAdded: p.creditAmountAdded
                }))
            });
            // 4. Validate credit balance for deletion (Edge Cases)
            this.loggerService.log('🛡️ Performing credit balance validation', {
                ownerId: paymentDetail.ownerId,
                relatedPaymentsCount: relatedPayments.length
            });
            await this.validateCreditBalanceForDeletion(queryRunner, relatedPayments, paymentDetail.ownerId);
            let totalAmountReversal = 0;
            let totalCreditReversal = 0;
            const invoicesToUpdate = new Map();
            // 5. Process each related payment for deletion
            this.loggerService.log('💰 Starting financial impact calculations', {
                totalRelatedPayments: relatedPayments.length
            });
            for (const relatedPayment of relatedPayments) {
                this.loggerService.log(`🔄 Processing payment ${relatedPayment.id}`, {
                    paymentId: relatedPayment.id,
                    type: relatedPayment.type,
                    amount: relatedPayment.amount,
                    isCreditUsed: relatedPayment.isCreditUsed,
                    creditAmountUsed: relatedPayment.creditAmountUsed,
                    isCreditsAdded: relatedPayment.isCreditsAdded,
                    creditAmountAdded: relatedPayment.creditAmountAdded,
                    invoiceId: relatedPayment.invoiceId
                });
                // Store the before state for audit logging
                const beforeState = { ...relatedPayment };
                delete beforeState.invoice; // Remove circular reference for JSON storage
                // Calculate financial impact
                const paymentAmount = Number(relatedPayment.amount || 0);
                const creditAmountUsed = Number(relatedPayment.creditAmountUsed || 0);
                const creditAmountAdded = Number(relatedPayment.creditAmountAdded || 0);
                totalAmountReversal += paymentAmount;
                totalCreditReversal += creditAmountUsed;
                this.loggerService.log('📊 Financial impact for this payment', {
                    paymentId: relatedPayment.id,
                    paymentAmount,
                    creditAmountUsed,
                    creditAmountAdded,
                    runningTotalAmountReversal: totalAmountReversal,
                    runningTotalCreditReversal: totalCreditReversal
                });
                // Group invoice updates
                if (relatedPayment.invoiceId && relatedPayment.invoice) {
                    const invoiceTotal = paymentAmount + creditAmountUsed;
                    if (invoicesToUpdate.has(relatedPayment.invoiceId)) {
                        const existing = invoicesToUpdate.get(relatedPayment.invoiceId);
                        existing.totalReversal += invoiceTotal;
                        this.loggerService.log('📋 Updated existing invoice reversal', {
                            invoiceId: relatedPayment.invoiceId,
                            previousTotal: existing.totalReversal - invoiceTotal,
                            additionalReversal: invoiceTotal,
                            newTotal: existing.totalReversal
                        });
                    }
                    else {
                        invoicesToUpdate.set(relatedPayment.invoiceId, {
                            totalReversal: invoiceTotal,
                            invoice: relatedPayment.invoice
                        });
                        this.loggerService.log('📋 Added new invoice for reversal', {
                            invoiceId: relatedPayment.invoiceId,
                            totalReversal: invoiceTotal,
                            invoiceAmount: relatedPayment.invoice.invoiceAmount,
                            currentPaidAmount: relatedPayment.invoice.paidAmount
                        });
                    }
                }
                // Reverse credit transactions for this payment
                if (relatedPayment.isCreditUsed && relatedPayment.creditAmountUsed > 0) {
                    this.loggerService.log('💳 Reversing credit usage (restoring credits)', {
                        paymentId: relatedPayment.id,
                        creditAmountToRestore: relatedPayment.creditAmountUsed,
                        ownerId: relatedPayment.ownerId
                    });
                    // Create a reverse credit transaction to restore the credits (ADD them back)
                    await this.createCreditTransaction(queryRunner, relatedPayment.ownerId, Number(relatedPayment.creditAmountUsed), enum_credit_transaction_type_1.CreditTransactionType.ADD, `Credit reversal for deleted payment ${relatedPayment.referenceAlphaId} - restoring used credits`, {
                        paymentDetailId: relatedPayment.id,
                        invoiceId: relatedPayment.invoiceId
                    }, relatedPayment.clinicId, relatedPayment.brandId, userId);
                }
                // If payment added credits, reverse them (USE them to remove)
                if (relatedPayment.isCreditsAdded && relatedPayment.creditAmountAdded > 0) {
                    this.loggerService.log('💰 Reversing credit addition (removing credits)', {
                        paymentId: relatedPayment.id,
                        creditAmountToRemove: relatedPayment.creditAmountAdded,
                        ownerId: relatedPayment.ownerId
                    });
                    await this.createCreditTransaction(queryRunner, relatedPayment.ownerId, Number(relatedPayment.creditAmountAdded), enum_credit_transaction_type_1.CreditTransactionType.USE, `Credit reversal for deleted payment ${relatedPayment.referenceAlphaId} - removing added credits`, {
                        paymentDetailId: relatedPayment.id,
                        invoiceId: relatedPayment.invoiceId
                    }, relatedPayment.clinicId, relatedPayment.brandId, userId);
                }
                // Log the deletion in audit trail for each payment
                const operations = [
                    {
                        type: 'DELETE_RECEIPT',
                        description: relatedPayment.id === paymentDetailId
                            ? 'Receipt deleted (main)'
                            : 'Receipt deleted (related)',
                        reason: deleteDto.comment
                    }
                ];
                this.loggerService.log('📝 Creating audit log entry', {
                    paymentId: relatedPayment.id,
                    operationType: 'DELETE',
                    isMainPayment: relatedPayment.id === paymentDetailId
                });
                await this.logChange(relatedPayment.id, userId, payment_details_audit_log_entity_1.PaymentDetailsAuditLogOperationType.DELETE, { before: beforeState, after: {} }, operations, queryRunner, deleteDto.comment);
                // Soft delete the payment detail
                this.loggerService.log('🗑️ Soft deleting payment', {
                    paymentId: relatedPayment.id
                });
                await queryRunner.manager.softDelete(payment_details_entity_1.PaymentDetailsEntity, { id: relatedPayment.id });
                this.loggerService.log('✅ Related payment deleted successfully', {
                    paymentId: relatedPayment.id,
                    type: relatedPayment.type,
                    amount: relatedPayment.amount,
                    creditAmountUsed: relatedPayment.creditAmountUsed,
                    creditAmountAdded: relatedPayment.creditAmountAdded
                });
            }
            // 6. Update invoice statuses for all affected invoices
            this.loggerService.log('📋 Starting invoice status updates', {
                totalInvoicesToUpdate: invoicesToUpdate.size,
                invoiceIds: Array.from(invoicesToUpdate.keys())
            });
            for (const [invoiceId, { totalReversal, invoice }] of invoicesToUpdate) {
                this.loggerService.log('🔄 Processing invoice status update', {
                    invoiceId,
                    currentStatus: invoice.status,
                    currentPaidAmount: invoice.paidAmount,
                    currentBalanceDue: invoice.balanceDue,
                    totalReversal,
                    invoiceAmount: invoice.invoiceAmount
                });
                const newPaidAmount = Math.max(0, Number(invoice.paidAmount || 0) - totalReversal);
                const newBalanceDue = Number(invoice.invoiceAmount) - newPaidAmount;
                // Determine new status
                const newStatus = newPaidAmount === 0
                    ? enum_invoice_status_1.EnumInvoiceStatus.PENDING
                    : newBalanceDue <= 0.01
                        ? enum_invoice_status_1.EnumInvoiceStatus.FULLY_PAID
                        : enum_invoice_status_1.EnumInvoiceStatus.PARTIALLY_PAID;
                this.loggerService.log('📊 Calculated new invoice values', {
                    invoiceId,
                    calculation: {
                        previousPaidAmount: invoice.paidAmount,
                        minus_totalReversal: totalReversal,
                        equals_newPaidAmount: newPaidAmount,
                        invoiceAmount: invoice.invoiceAmount,
                        minus_newPaidAmount: newPaidAmount,
                        equals_newBalanceDue: newBalanceDue
                    },
                    statusLogic: {
                        condition: newPaidAmount === 0 ? 'PENDING' : newBalanceDue <= 0.01 ? 'FULLY_PAID' : 'PARTIALLY_PAID',
                        newStatus
                    }
                });
                await queryRunner.manager.update(invoice_entity_1.InvoiceEntity, { id: invoiceId }, {
                    status: newStatus,
                    paidAmount: newPaidAmount,
                    balanceDue: newBalanceDue,
                    updatedBy: userId,
                    updatedAt: new Date()
                });
                this.loggerService.log('✅ Invoice status updated successfully', {
                    invoiceId,
                    changes: {
                        status: { from: invoice.status, to: newStatus },
                        paidAmount: { from: invoice.paidAmount, to: newPaidAmount },
                        balanceDue: { from: invoice.balanceDue, to: newBalanceDue }
                    },
                    totalReversal
                });
            }
            this.loggerService.log('💾 Committing transaction', {
                referenceAlphaId: paymentDetail.referenceAlphaId,
                summary: {
                    totalPaymentsDeleted: relatedPayments.length,
                    totalAmountReversal,
                    totalCreditReversal,
                    totalInvoicesUpdated: invoicesToUpdate.size
                }
            });
            await queryRunner.commitTransaction();
            this.loggerService.log('🎉 Payment deletion completed successfully', {
                referenceAlphaId: paymentDetail.referenceAlphaId,
                originalPaymentId: paymentDetailId,
                totalPaymentsDeleted: relatedPayments.length,
                deletedPaymentDetails: relatedPayments.map(p => ({
                    id: p.id,
                    type: p.type,
                    amount: p.amount,
                    creditAmountUsed: p.creditAmountUsed,
                    creditAmountAdded: p.creditAmountAdded
                })),
                financialImpact: {
                    totalAmountReversal,
                    totalCreditReversal,
                    invoicesAffected: Array.from(invoicesToUpdate.keys())
                },
                auditInfo: {
                    userId,
                    comment: deleteDto.comment,
                    timestamp: new Date().toISOString()
                }
            });
            return {
                message: `Successfully deleted ${relatedPayments.length} related payment detail(s) and reversed all financial impacts`
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            const errorType = ((_a = error === null || error === void 0 ? void 0 : error.constructor) === null || _a === void 0 ? void 0 : _a.name) || 'Unknown';
            this.loggerService.error('❌ Error occurred during payment deletion - rolling back transaction', {
                paymentDetailId,
                error: errorMessage,
                stack: errorStack,
                errorType,
                userId,
                deleteDto
            });
            await queryRunner.rollbackTransaction();
            this.loggerService.error('🔄 Transaction rolled back due to error', {
                paymentDetailId,
                errorMessage
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to delete payment detail', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
        finally {
            this.loggerService.log('🔚 Releasing database connection', {
                paymentDetailId
            });
            await queryRunner.release();
        }
    }
};
exports.PaymentDetailsService = PaymentDetailsService;
exports.PaymentDetailsService = PaymentDetailsService = PaymentDetailsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_details_entity_1.PaymentDetailsEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(payment_details_audit_log_entity_1.PaymentDetailsAuditLogEntity)),
    __param(7, (0, typeorm_1.InjectRepository)(clinic_entity_1.ClinicEntity)),
    __param(8, (0, typeorm_1.InjectRepository)(patient_owner_entity_1.PatientOwner)),
    __param(9, (0, typeorm_1.InjectRepository)(invoice_entity_1.InvoiceEntity)),
    __param(10, (0, typeorm_1.InjectRepository)(invoice_audit_log_entity_1.InvoiceAuditLogEntity)),
    __param(11, (0, typeorm_1.InjectRepository)(owner_brand_entity_1.OwnerBrand)),
    __param(12, (0, typeorm_1.InjectRepository)(credit_transaction_entity_1.CreditTransactionEntity)),
    __param(14, (0, common_1.Inject)((0, common_1.forwardRef)(() => sqs_service_1.SqsService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        patients_service_1.PatientsService,
        s3_service_1.S3Service,
        send_mail_service_1.SESMailService,
        whatsapp_service_1.WhatsappService,
        winston_logger_service_1.WinstonLogger,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Connection,
        sqs_service_1.SqsService])
], PaymentDetailsService);
//# sourceMappingURL=payment-details.service.js.map