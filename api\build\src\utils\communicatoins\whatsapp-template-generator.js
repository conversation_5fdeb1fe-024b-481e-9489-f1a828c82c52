"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPetTransferNewOwnerClinicLinkTemplateData = exports.getPetTransferNewOwnerTemplateData = exports.getPetTransferOldOwnerTemplateData = exports.getLedgerDocumentClinicLinkTemplateData = exports.getLedgerDocumentTemplateData = exports.reminderNotificationsClinicLink = exports.reminderNotifications = exports.sendSupportingDocumentImage = exports.sendSupportingDocumentPdf = exports.sendtreatmentEstimateDocumentClinicLink = exports.sendtreatmentEstimateDocument = exports.sendTreatmentEstimateUrl = exports.sendMedicalRecordImage = exports.sendMedicalRecordDocument = exports.getVaccinationImageClinicLinkTemplateData = exports.getVaccinationImageTemplateData = exports.sendDiagnosticReportDocument = exports.sendDiagnosticReportImage = exports.sendNonSignableDocumentClinicLink = exports.sendNonSignableDocument = exports.sendSignableDocumentUrl = exports.getIndividualEMRClinicLinkTemplateData = exports.getIndividualEMRTemplateData = exports.getAppointmentPrescriptionClinicLinkTemplateData = exports.getAppointmentPrescriptionTemplateData = exports.getAppointmentInvoiceClinicLinkTemplateData = exports.getAppointmentInvoiceTemplateData = exports.getAppointmentVaccinationCertificateClinicLinkTemplateData = exports.getAppointmentVaccinationCertificateTemplateData = exports.getCreditNoteGenerationClinicLinkTemplateData = exports.getCreditNoteGenerationTemplateData = exports.getReceiptGenerationClinicLinkTemplateData = exports.getReceiptGenerationTemplateData = exports.getAppointmentCancellationClinicLinkTemplateData = exports.getAppointmentCancellationTemplateData = exports.getAppointmentReminderClinicLinkTemplateData = exports.getAppointmentReminderTemplateData = exports.getAppointmentUpdateClinicLinkTemplateData = exports.getAppointmentUpdateTemplateData = exports.getAppointmentCreatationClinicLinkTemplateData = exports.getAppointmentCreatationTemplateData = exports.whatsappTemplateTypes = void 0;
exports.whatsappTemplateTypes = {
    APPOINTMENT_CONFIRMATION: 'appointment_confirmation_v3',
    APPOINTMENT_CONFIRMATION_CLINICLINK: 'appointment_confirmation_cliniclink_v4',
    APPOINTMENT_UPDATE: 'appointment_update_v2',
    APPOINTMENT_UPDATE_CLINICLINK: 'appointment_update_cliniclink_v3',
    APPOINTMENT_REMINDER: 'appointment_reminder_v2',
    APPOINTMENT_REMINDER_CLINICLINK: 'appointment_reminder_cliniclink_v3',
    APPOINTMENT_CANCELLATION: 'appointment_cancellation_v2',
    APPOINTMENT_CANCELLATION_CLINICLINK: 'appointment_cancellation_cliniclink_v3',
    RECEIPT_GENERATION: 'receipt_generated',
    RECEIPT_GENERATION_CLINICLINK: 'receipt_generated_cliniclink_v2',
    CREDIT_NOTE_GENERATION: 'credit_note_generated_v1',
    CREDIT_NOTE_GENERATION_CLINICLINK: 'credit_note_generated_cliniclink_v2',
    VACCINATION_GENERATION: 'appointment_completion_vaccination_v1',
    VACCINATION_GENERATION_CLINICLINK: 'appointment_completion_vaccination_cliniclink_v2',
    APPOINTMENT_COMPLETION_INVOICE: 'appointment_completion_invoice_v1',
    APPOINTMENT_COMPLETION_INVOICE_CLINICLINK: 'appointment_completion_invoice_cliniclink_v2',
    APPOINTMENT_COMPLETION_PRESCRIPTION: 'prescription_v1',
    APPOINTMENT_COMPLETION_PRESCRIPTION_CLINICLINK: 'prescription_cliniclink_v2',
    MEDICAL_RECORDS: 'medical_record_v3',
    MEDICAL_RECORDS_CLINICLINK: 'medical_record_cliniclink_v4',
    SEND_SIGNABLE_DOCUMENT: 'send_signable_document',
    SEND_NON_SIGNABLE_DOCUMENT: 'send_non_signable_document',
    SEND_NON_SIGNABLE_DOCUMENT_CLINICLINK: 'send_non_signable_document_cliniclink_v4',
    DIAGNOSTIC_REPORT_IMAGE: 'diagnostic_report_image_v2',
    DIAGNOSTIC_REPORT_PDF: 'diagnostic_report_pdf_v4',
    VACCINATION_IMAGE: 'vaccination_image_v2',
    VACCINATION_IMAGE_CLINICLINK: 'vaccination_image_cliniclink_v5',
    MEDICAL_RECORD_PDF: 'medical_record_pdf',
    MEDICAL_RECORD_IMAGE: 'medical_record_image_v2',
    SUPPORTING_DOCUMENT_PDF: 'supporting_document_pdf',
    SUPPORTING_DOCUMENT_IMAGE: 'supporting_document_image',
    REMINDER_NOTIFICATION: 'reminder_notification',
    REMINDER_NOTIFICATION_CLINICLINK: 'reminder_notification_cliniclink_v3',
    TREATMENT_ESTIMATE_DOCUMENT: 'treatment_estimate_document',
    TREATMENT_ESTIMATE_DOCUMENT_CLINICLINK: 'treatment_estimate_document_cliniclink_v4',
    TREATMENT_ESTIMATE_URL: 'treatment_estimate_url',
    LEDGER_DOCUMENT: 'ledger_document_v1',
    LEDGER_DOCUMENT_CLINICLINK: 'ledger_document_cliniclink_v3',
    PET_TRANSFER_OLD_OWNER: 'transfer_oldowner_v1',
    PET_TRANSFER_NEW_OWNER: 'transfer_newowner',
    PET_TRANSFER_NEW_OWNER_CLINICLINK: 'transfer_newowner_cliniclink_v3'
};
const getAppointmentCreatationTemplateData = ({ clientName, appointmentTime, appointmentDate, brandName, contactInformation, mobileNumber, clinicAddress }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_CONFIRMATION,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'appointment_date',
            value: appointmentDate
        },
        {
            name: 'appointment_time',
            value: appointmentTime
        },
        {
            name: 'contact_information',
            value: contactInformation
        },
        {
            name: 'clinic_address',
            value: clinicAddress
        }
    ]
});
exports.getAppointmentCreatationTemplateData = getAppointmentCreatationTemplateData;
const getAppointmentCreatationClinicLinkTemplateData = ({ clientName, appointmentTime, appointmentDate, brandName, contactInformation, mobileNumber, clinicAddress, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_CONFIRMATION_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'appointment_date',
            value: appointmentDate
        },
        {
            name: 'appointment_time',
            value: appointmentTime
        },
        {
            name: 'contact_information',
            value: contactInformation
        },
        {
            name: 'clinic_address',
            value: clinicAddress
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getAppointmentCreatationClinicLinkTemplateData = getAppointmentCreatationClinicLinkTemplateData;
const getAppointmentUpdateTemplateData = ({ clientName, appointmentTime, appointmentDate, brandName, contactInformation, mobileNumber, petName }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_UPDATE,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'updated_appointment_date',
            value: appointmentDate
        },
        {
            name: 'updated_appointment_time',
            value: appointmentTime
        },
        {
            name: 'contact_information',
            value: contactInformation
        },
        {
            name: 'pet_name',
            value: petName
        }
    ]
});
exports.getAppointmentUpdateTemplateData = getAppointmentUpdateTemplateData;
const getAppointmentUpdateClinicLinkTemplateData = ({ clientName, appointmentTime, appointmentDate, brandName, contactInformation, mobileNumber, petName, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_UPDATE_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'updated_appointment_date',
            value: appointmentDate
        },
        {
            name: 'updated_appointment_time',
            value: appointmentTime
        },
        {
            name: 'contact_information',
            value: contactInformation
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getAppointmentUpdateClinicLinkTemplateData = getAppointmentUpdateClinicLinkTemplateData;
const getAppointmentReminderTemplateData = ({ clientName, appointmentTime, appointmentDate, brandName, contactInformation, mobileNumber, petName }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_REMINDER,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'appointment_date',
            value: appointmentDate
        },
        {
            name: 'appointment_time',
            value: appointmentTime
        },
        {
            name: 'contact_information',
            value: contactInformation
        },
        {
            name: 'pet_name',
            value: petName
        }
    ]
});
exports.getAppointmentReminderTemplateData = getAppointmentReminderTemplateData;
const getAppointmentReminderClinicLinkTemplateData = ({ clientName, appointmentTime, appointmentDate, brandName, contactInformation, mobileNumber, petName, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_REMINDER_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'appointment_date',
            value: appointmentDate
        },
        {
            name: 'appointment_time',
            value: appointmentTime
        },
        {
            name: 'contact_information',
            value: contactInformation
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getAppointmentReminderClinicLinkTemplateData = getAppointmentReminderClinicLinkTemplateData;
const getAppointmentCancellationTemplateData = ({ clientName, appointmentTime, appointmentDate, brandName, contactInformation, mobileNumber }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_CANCELLATION,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'appointment_date',
            value: appointmentDate
        },
        {
            name: 'appointment_time',
            value: appointmentTime
        },
        {
            name: 'contact_information',
            value: contactInformation
        }
    ]
});
exports.getAppointmentCancellationTemplateData = getAppointmentCancellationTemplateData;
const getAppointmentCancellationClinicLinkTemplateData = ({ clientName, appointmentTime, appointmentDate, brandName, contactInformation, mobileNumber, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_CANCELLATION_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'appointment_date',
            value: appointmentDate
        },
        {
            name: 'appointment_time',
            value: appointmentTime
        },
        {
            name: 'contact_information',
            value: contactInformation
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getAppointmentCancellationClinicLinkTemplateData = getAppointmentCancellationClinicLinkTemplateData;
const getReceiptGenerationTemplateData = ({ clientName, receiptDate, brandName, receiptFile, mobileNumber }) => ({
    templateName: exports.whatsappTemplateTypes.RECEIPT_GENERATION,
    mobileNumber,
    valuesArray: [
        {
            name: 'Client_Name',
            value: clientName
        },
        {
            name: 'Brand_Name',
            value: brandName
        },
        {
            name: 'receipt_generation_date',
            value: receiptDate
        },
        {
            name: 'receipt_file',
            value: receiptFile
        }
    ]
});
exports.getReceiptGenerationTemplateData = getReceiptGenerationTemplateData;
const getReceiptGenerationClinicLinkTemplateData = ({ clientName, receiptDate, brandName, receiptFile, mobileNumber, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.RECEIPT_GENERATION_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'Client_Name',
            value: clientName
        },
        {
            name: 'Brand_Name',
            value: brandName
        },
        {
            name: 'receipt_generation_date',
            value: receiptDate
        },
        {
            name: 'receipt_file',
            value: receiptFile
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getReceiptGenerationClinicLinkTemplateData = getReceiptGenerationClinicLinkTemplateData;
const getCreditNoteGenerationTemplateData = ({ clientName, refundDate, brandName, creditNoteFile, mobileNumber }) => ({
    templateName: exports.whatsappTemplateTypes.CREDIT_NOTE_GENERATION,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'refund_processing_date',
            value: refundDate
        },
        {
            name: 'credit_note_file',
            value: creditNoteFile
        }
    ]
});
exports.getCreditNoteGenerationTemplateData = getCreditNoteGenerationTemplateData;
const getCreditNoteGenerationClinicLinkTemplateData = ({ clientName, refundDate, brandName, creditNoteFile, mobileNumber, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.CREDIT_NOTE_GENERATION_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'refund_processing_date',
            value: refundDate
        },
        {
            name: 'credit_note_file',
            value: creditNoteFile
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getCreditNoteGenerationClinicLinkTemplateData = getCreditNoteGenerationClinicLinkTemplateData;
const getAppointmentVaccinationCertificateTemplateData = ({ clientName, petName, brandName, vaccinationFile, mobileNumber }) => ({
    templateName: exports.whatsappTemplateTypes.VACCINATION_GENERATION,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'vaccination_file',
            value: vaccinationFile
        }
    ]
});
exports.getAppointmentVaccinationCertificateTemplateData = getAppointmentVaccinationCertificateTemplateData;
const getAppointmentVaccinationCertificateClinicLinkTemplateData = ({ clientName, petName, brandName, vaccinationFile, mobileNumber, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.VACCINATION_GENERATION_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'vaccination_file',
            value: vaccinationFile
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getAppointmentVaccinationCertificateClinicLinkTemplateData = getAppointmentVaccinationCertificateClinicLinkTemplateData;
const getAppointmentInvoiceTemplateData = ({ clientName, petName, brandName, doctorName, invoiceFile, mobileNumber }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_COMPLETION_INVOICE,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'doctor_name',
            value: doctorName
        },
        {
            name: 'invoice_file',
            value: invoiceFile
        }
    ]
});
exports.getAppointmentInvoiceTemplateData = getAppointmentInvoiceTemplateData;
const getAppointmentInvoiceClinicLinkTemplateData = ({ clientName, petName, brandName, doctorName, invoiceFile, mobileNumber, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_COMPLETION_INVOICE_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'doctor_name',
            value: doctorName
        },
        {
            name: 'invoice_file',
            value: invoiceFile
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getAppointmentInvoiceClinicLinkTemplateData = getAppointmentInvoiceClinicLinkTemplateData;
const getAppointmentPrescriptionTemplateData = ({ clientName, petName, brandName, doctorName, prescriptionFile, mobileNumber }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_COMPLETION_PRESCRIPTION,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'doctor_name',
            value: doctorName
        },
        {
            name: 'prescription_file',
            value: prescriptionFile
        }
    ]
});
exports.getAppointmentPrescriptionTemplateData = getAppointmentPrescriptionTemplateData;
const getAppointmentPrescriptionClinicLinkTemplateData = ({ clientName, petName, brandName, doctorName, prescriptionFile, mobileNumber, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.APPOINTMENT_COMPLETION_PRESCRIPTION_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'doctor_name',
            value: doctorName
        },
        {
            name: 'prescription_file',
            value: prescriptionFile
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getAppointmentPrescriptionClinicLinkTemplateData = getAppointmentPrescriptionClinicLinkTemplateData;
const getIndividualEMRTemplateData = ({ clientName, petName, brandName, EMRFile, mobileNumber, clinicContact }) => ({
    templateName: exports.whatsappTemplateTypes.MEDICAL_RECORDS,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'record_pdf',
            value: EMRFile
        },
        {
            name: 'contact_information',
            value: clinicContact
        }
    ]
});
exports.getIndividualEMRTemplateData = getIndividualEMRTemplateData;
const getIndividualEMRClinicLinkTemplateData = ({ clientName, petName, brandName, EMRFile, mobileNumber, clinicContact, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.MEDICAL_RECORDS_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'record_pdf',
            value: EMRFile
        },
        {
            name: 'contact_information',
            value: clinicContact
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getIndividualEMRClinicLinkTemplateData = getIndividualEMRClinicLinkTemplateData;
const sendSignableDocumentUrl = ({ clientName, brandName, url, mobileNumber, patientName }) => ({
    templateName: exports.whatsappTemplateTypes.SEND_SIGNABLE_DOCUMENT,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'patient_name',
            value: patientName
        },
        {
            name: 'url',
            value: url
        }
    ]
});
exports.sendSignableDocumentUrl = sendSignableDocumentUrl;
const sendNonSignableDocument = ({ clientName, brandName, contactNo, mobileNumber, patientName, documentName, documentUrl }) => ({
    templateName: exports.whatsappTemplateTypes.SEND_NON_SIGNABLE_DOCUMENT,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'patient_name',
            value: patientName
        },
        {
            name: 'contact_no',
            value: contactNo
        },
        {
            name: 'document_name',
            value: documentName
        },
        {
            name: 'document_url',
            value: documentUrl
        }
    ]
});
exports.sendNonSignableDocument = sendNonSignableDocument;
const sendNonSignableDocumentClinicLink = ({ clientName, brandName, contactNo, mobileNumber, patientName, documentName, documentUrl, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.SEND_NON_SIGNABLE_DOCUMENT_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'patient_name',
            value: patientName
        },
        {
            name: 'contact_no',
            value: contactNo
        },
        {
            name: 'document_name',
            value: documentName
        },
        {
            name: 'document_url',
            value: documentUrl
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.sendNonSignableDocumentClinicLink = sendNonSignableDocumentClinicLink;
const sendDiagnosticReportImage = ({ clientName, mobileNumber, documentUrl, petName }) => ({
    templateName: exports.whatsappTemplateTypes.DIAGNOSTIC_REPORT_IMAGE,
    mobileNumber,
    valuesArray: [
        {
            name: 'name',
            value: clientName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'document_url',
            value: documentUrl
        }
    ]
});
exports.sendDiagnosticReportImage = sendDiagnosticReportImage;
const sendDiagnosticReportDocument = ({ clientName, mobileNumber, documentUrl, petName }) => ({
    templateName: exports.whatsappTemplateTypes.DIAGNOSTIC_REPORT_PDF,
    mobileNumber,
    valuesArray: [
        {
            name: 'name',
            value: clientName
        },
        {
            name: 'document_url',
            value: documentUrl
        },
        {
            name: 'pet_name',
            value: petName
        }
    ]
});
exports.sendDiagnosticReportDocument = sendDiagnosticReportDocument;
const getVaccinationImageTemplateData = ({ clientName, petName, brandName, EMRFile, mobileNumber, clinicContact }) => ({
    templateName: exports.whatsappTemplateTypes.VACCINATION_IMAGE,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'record_pdf',
            value: EMRFile
        },
        {
            name: 'contact_information',
            value: clinicContact
        }
    ]
});
exports.getVaccinationImageTemplateData = getVaccinationImageTemplateData;
const getVaccinationImageClinicLinkTemplateData = ({ clientName, petName, brandName, EMRFile, mobileNumber, clinicContact, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.VACCINATION_IMAGE_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'record_pdf',
            value: EMRFile
        },
        {
            name: 'contact_information',
            value: clinicContact
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getVaccinationImageClinicLinkTemplateData = getVaccinationImageClinicLinkTemplateData;
const sendMedicalRecordDocument = ({ clientName, mobileNumber, documentUrl, petName }) => ({
    templateName: exports.whatsappTemplateTypes.MEDICAL_RECORD_PDF,
    mobileNumber,
    valuesArray: [
        {
            name: 'name',
            value: clientName
        },
        {
            name: 'document_url',
            value: documentUrl
        },
        {
            name: 'pet_name',
            value: petName
        }
    ]
});
exports.sendMedicalRecordDocument = sendMedicalRecordDocument;
const sendMedicalRecordImage = ({ clientName, mobileNumber, documentUrl, petName }) => ({
    templateName: exports.whatsappTemplateTypes.MEDICAL_RECORD_IMAGE,
    mobileNumber,
    valuesArray: [
        {
            name: 'name',
            value: clientName
        },
        {
            name: 'document_url',
            value: documentUrl
        },
        {
            name: 'pet_name',
            value: petName
        }
    ]
});
exports.sendMedicalRecordImage = sendMedicalRecordImage;
const sendTreatmentEstimateUrl = ({ clientName, brandName, url, mobileNumber, patientName }) => ({
    templateName: exports.whatsappTemplateTypes.TREATMENT_ESTIMATE_URL,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'patient_name',
            value: patientName
        },
        {
            name: 'url',
            value: url
        }
    ]
});
exports.sendTreatmentEstimateUrl = sendTreatmentEstimateUrl;
const sendtreatmentEstimateDocument = ({ clientName, brandName, contactNo, mobileNumber, patientName, documentName, documentUrl }) => ({
    templateName: exports.whatsappTemplateTypes.TREATMENT_ESTIMATE_DOCUMENT,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'patient_name',
            value: patientName
        },
        {
            name: 'contact_no',
            value: contactNo
        },
        {
            name: 'document_name',
            value: documentName
        },
        {
            name: 'document_url',
            value: documentUrl
        }
    ]
});
exports.sendtreatmentEstimateDocument = sendtreatmentEstimateDocument;
const sendtreatmentEstimateDocumentClinicLink = ({ clientName, brandName, contactNo, mobileNumber, patientName, documentName, documentUrl, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.TREATMENT_ESTIMATE_DOCUMENT_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'patient_name',
            value: patientName
        },
        {
            name: 'contact_no',
            value: contactNo
        },
        {
            name: 'document_name',
            value: documentName
        },
        {
            name: 'document_url',
            value: documentUrl
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.sendtreatmentEstimateDocumentClinicLink = sendtreatmentEstimateDocumentClinicLink;
const sendSupportingDocumentPdf = ({ clientName, mobileNumber, documentUrl }) => ({
    templateName: exports.whatsappTemplateTypes.SUPPORTING_DOCUMENT_PDF,
    mobileNumber,
    valuesArray: [
        {
            name: 'name',
            value: clientName
        },
        {
            name: 'document_url',
            value: documentUrl
        }
    ]
});
exports.sendSupportingDocumentPdf = sendSupportingDocumentPdf;
const sendSupportingDocumentImage = ({ clientName, mobileNumber, documentUrl }) => ({
    templateName: exports.whatsappTemplateTypes.SUPPORTING_DOCUMENT_IMAGE,
    mobileNumber,
    valuesArray: [
        {
            name: 'name',
            value: clientName
        },
        {
            name: 'document_url',
            value: documentUrl
        }
    ]
});
exports.sendSupportingDocumentImage = sendSupportingDocumentImage;
const reminderNotifications = ({ clientName, dueDate, title, brand, petName, mobileNumber }) => ({
    templateName: exports.whatsappTemplateTypes.REMINDER_NOTIFICATION,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'title',
            value: title
        },
        {
            name: 'due_date',
            value: dueDate
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'brand_name',
            value: brand
        }
    ]
});
exports.reminderNotifications = reminderNotifications;
const reminderNotificationsClinicLink = ({ clientName, dueDate, title, brand, petName, mobileNumber, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.REMINDER_NOTIFICATION_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'title',
            value: title
        },
        {
            name: 'due_date',
            value: dueDate
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'brand_name',
            value: brand
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.reminderNotificationsClinicLink = reminderNotificationsClinicLink;
const getLedgerDocumentTemplateData = ({ clientName, brandName, EMRFile, mobileNumber, clinicContact }) => ({
    templateName: exports.whatsappTemplateTypes.LEDGER_DOCUMENT,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'record_pdf',
            value: EMRFile
        },
        {
            name: 'contact_information',
            value: clinicContact
        }
    ]
});
exports.getLedgerDocumentTemplateData = getLedgerDocumentTemplateData;
const getLedgerDocumentClinicLinkTemplateData = ({ clientName, brandName, EMRFile, mobileNumber, clinicContact, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.LEDGER_DOCUMENT_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'client_name',
            value: clientName
        },
        {
            name: 'brand_name',
            value: brandName
        },
        {
            name: 'record_pdf',
            value: EMRFile
        },
        {
            name: 'contact_information',
            value: clinicContact
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        }
    ]
});
exports.getLedgerDocumentClinicLinkTemplateData = getLedgerDocumentClinicLinkTemplateData;
const getPetTransferOldOwnerTemplateData = ({ oldPetOwner, petName, newPetOwner, brandName, mobileNumber }) => ({
    templateName: exports.whatsappTemplateTypes.PET_TRANSFER_OLD_OWNER,
    mobileNumber,
    valuesArray: [
        {
            name: 'initial_pet_owner',
            value: oldPetOwner
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'new_pet_owner',
            value: newPetOwner
        },
        {
            name: 'Brand_Name',
            value: brandName
        }
    ]
});
exports.getPetTransferOldOwnerTemplateData = getPetTransferOldOwnerTemplateData;
const getPetTransferNewOwnerTemplateData = ({ oldPetOwner, petName, newPetOwner, brandName, mobileNumber }) => ({
    templateName: exports.whatsappTemplateTypes.PET_TRANSFER_NEW_OWNER,
    mobileNumber,
    valuesArray: [
        {
            name: 'new_pet_owner',
            value: newPetOwner
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'initial_pet_owner',
            value: oldPetOwner
        },
        {
            name: 'Brand_Name',
            value: brandName
        }
    ]
});
exports.getPetTransferNewOwnerTemplateData = getPetTransferNewOwnerTemplateData;
const getPetTransferNewOwnerClinicLinkTemplateData = ({ oldPetOwner, petName, newPetOwner, brandName, mobileNumber, client_booking_URL }) => ({
    templateName: exports.whatsappTemplateTypes.PET_TRANSFER_NEW_OWNER_CLINICLINK,
    mobileNumber,
    valuesArray: [
        {
            name: 'new_pet_owner',
            value: newPetOwner
        },
        {
            name: 'pet_name',
            value: petName
        },
        {
            name: 'initial_pet_owner',
            value: oldPetOwner
        },
        {
            name: 'client_booking_URL',
            value: client_booking_URL
        },
        {
            name: 'Brand_Name',
            value: brandName
        }
    ]
});
exports.getPetTransferNewOwnerClinicLinkTemplateData = getPetTransferNewOwnerClinicLinkTemplateData;
//# sourceMappingURL=whatsapp-template-generator.js.map