import { Message } from '@aws-sdk/client-sqs';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../interfaces/queue-handler.interface';
import { <PERSON><PERSON>og<PERSON> } from '../../../logger/winston-logger.service';
import { S3Service } from '../../s3/s3.service';
import { SESMailService } from '../../ses/send-mail-service';
import { WhatsappService } from '../../../whatsapp-integration/whatsapp.service';
import { PatientVaccination } from '../../../../patient-vaccinations/entities/patient-vaccinations.entity';
import { Repository } from 'typeorm';
import { Patient } from '../../../../patients/entities/patient.entity';
import { PatientRemindersService } from '../../../../patient-reminders/patient-reminder.service';
import { GlobalReminderService } from '../../../../patient-global-reminders/global-reminders.service';
import { ClinicProductEntity } from '../../../../clinic-products/entities/clinic-product.entity';
import { ClinicVaccinationEntity } from '../../../../clinic-vaccinations/entities/clinic-vaccination.entity';
import { ClinicMedicationEntity } from '../../../../clinic-medications/entities/clinic-medication.entity';
import { ClinicConsumableEntity } from '../../../../clinic-consumables/entities/clinic-consumable.entity';
import { InvoiceEntity } from '../../../../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../../../../payment-details/entities/payment-details.entity';
import { ClinicEntity } from '../../../../clinics/entities/clinic.entity';
import { OwnerBrand } from '../../../../owners/entities/owner-brand.entity';
import { PatientsService } from '../../../../patients/patients.service';
import { ClinicLabReportService } from '../../../../clinic-lab-report/clinic-lab-report.service';
import { AppointmentDetailsEntity } from '../../../../appointments/entities/appointment-details.entity';
import { AppointmentDoctorsEntity } from '../../../../appointments/entities/appointment-doctor.entity';
export declare class ProcessInvoiceTasksHandler implements QueueHandler {
    private readonly logger;
    private readonly s3Service;
    private readonly mailService;
    private readonly whatsappService;
    private readonly patientVaccinationRepository;
    private readonly patientRepository;
    private readonly clinicProduct;
    private readonly clinicVaccination;
    private readonly clinicMedication;
    private readonly clinicConsumableEntity;
    private readonly invoiceRepository;
    private readonly paymentDetailsRepository;
    private readonly clinicRepository;
    private readonly ownerBrandRepository;
    private readonly patientRemindersService;
    private readonly globalReminderService;
    private readonly patientService;
    private readonly clinicLabReportService;
    private appointmentDetailsEntity;
    private appointmentDoctorsEntity;
    constructor(logger: WinstonLogger, s3Service: S3Service, mailService: SESMailService, whatsappService: WhatsappService, patientVaccinationRepository: Repository<PatientVaccination>, patientRepository: Repository<Patient>, clinicProduct: Repository<ClinicProductEntity>, clinicVaccination: Repository<ClinicVaccinationEntity>, clinicMedication: Repository<ClinicMedicationEntity>, clinicConsumableEntity: Repository<ClinicConsumableEntity>, invoiceRepository: Repository<InvoiceEntity>, paymentDetailsRepository: Repository<PaymentDetailsEntity>, clinicRepository: Repository<ClinicEntity>, ownerBrandRepository: Repository<OwnerBrand>, patientRemindersService: PatientRemindersService, globalReminderService: GlobalReminderService, patientService: PatientsService, clinicLabReportService: ClinicLabReportService, appointmentDetailsEntity: Repository<AppointmentDetailsEntity>, appointmentDoctorsEntity: Repository<AppointmentDoctorsEntity>);
    handle(message: Message): Promise<void>;
    downloadPDF(url: string): Promise<Buffer>;
    processReminders(data: any): Promise<void>;
    generatePrescriptionPdfs(data: any): Promise<void>;
    updateInventory(data: any): Promise<void>;
    generateVaccinationPdfs(data: any): Promise<void>;
    generateVaccination(data: any): Promise<void>;
    processInvoiceDocument(data: any): Promise<void>;
    private generateDocumentPdf;
    private shareInvoiceDocument;
    private getClinicAddress;
    private calculateAge;
    private sendMail;
    private generatePrescriptionPDF;
    private generateVaccinationPDF;
    private sendVaccinationEmail;
    private sendVaccinationWhatsApp;
    private distributeDocuments;
    private sendAppointmentConfirmationWhatsappMessage;
    private sendPrescriptionMessage;
    private sendAppointmentVaccinationWhatsappMessage;
    processPaymentDocument(data: any): Promise<void>;
    updatePatientDetails(data: any): Promise<void>;
    updateInventoryForInvoiceUpdate(data: any): Promise<void>;
}
