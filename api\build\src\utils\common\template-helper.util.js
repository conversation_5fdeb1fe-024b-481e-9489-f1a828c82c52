"use strict";
// Removed getClientBookingUrl import – WhatsA<PERSON> now needs only the brand slug
Object.defineProperty(exports, "__esModule", { value: true });
exports.getClientBookingSettings = getClientBookingSettings;
exports.selectTemplate = selectTemplate;
/**
 * Helper function to determine if client booking URL should be used
 * and returns the URL if applicable
 * @param clinic The clinic entity with customRule and brand
 * @returns Object with isClientBookingEnabled and clientBookingUrl (if available)
 */
function getClientBookingSettings(clinic) {
    var _a, _b, _c;
    // Check if client booking settings are enabled for the clinic
    const isClientBookingEnabled = !!((_b = (_a = clinic === null || clinic === void 0 ? void 0 : clinic.customRule) === null || _a === void 0 ? void 0 : _a.clientBookingSettings) === null || _b === void 0 ? void 0 : _b.isEnabled);
    // If enabled, return the brand slug instead of full URL (WhatsApp requirement)
    if (isClientBookingEnabled && ((_c = clinic === null || clinic === void 0 ? void 0 : clinic.brand) === null || _c === void 0 ? void 0 : _c.slug)) {
        const clientBookingUrl = clinic.brand.slug; // pass only slug
        return { isClientBookingEnabled, clientBookingUrl };
    }
    // Either not enabled or no slug available
    return { isClientBookingEnabled: false };
}
/**
 * Helper function to select appropriate template based on client booking settings
 * @param clinic The clinic entity to check settings for
 * @param baseArgs The base arguments for the template
 * @param standardTemplate Function to generate the standard template
 * @param clinicLinkTemplate Function to generate the clinic link template
 * @returns The appropriate template data
 */
function selectTemplate(clinic, baseArgs, standardTemplate, clinicLinkTemplate) {
    console.log('clinic', clinic);
    console.log('baseArgs', baseArgs);
    console.log('standardTemplate', standardTemplate);
    console.log('clinicLinkTemplate', clinicLinkTemplate);
    const { isClientBookingEnabled, clientBookingUrl } = getClientBookingSettings(clinic);
    if (isClientBookingEnabled && clientBookingUrl) {
        return clinicLinkTemplate({
            ...baseArgs,
            client_booking_URL: clientBookingUrl
        });
    }
    return standardTemplate(baseArgs);
}
//# sourceMappingURL=template-helper.util.js.map