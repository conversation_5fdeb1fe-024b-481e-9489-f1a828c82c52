// Removed getClientBookingUrl import – WhatsApp now needs only the brand slug

/**
 * Helper function to determine if client booking URL should be used
 * and returns the URL if applicable
 * @param clinic The clinic entity with customRule and brand
 * @returns Object with isClientBookingEnabled and clientBookingUrl (if available)
 */
export function getClientBookingSettings(clinic: any): {
	isClientBookingEnabled: boolean;
	clientBookingUrl?: string;
} {
	// Check if client booking settings are enabled for the clinic
	const isClientBookingEnabled =
		!!clinic?.customRule?.clientBookingSettings?.isEnabled;

	// If enabled, return the brand slug instead of full URL (WhatsApp requirement)
	if (isClientBookingEnabled && clinic?.brand?.slug) {
		const clientBookingUrl = clinic.brand.slug; // pass only slug
		return { isClientBookingEnabled, clientBookingUrl };
	}

	// Either not enabled or no slug available
	return { isClientBookingEnabled: false };
}

/**
 * Helper function to select appropriate template based on client booking settings
 * @param clinic The clinic entity to check settings for
 * @param baseArgs The base arguments for the template
 * @param standardTemplate Function to generate the standard template
 * @param clinicLinkTemplate Function to generate the clinic link template
 * @returns The appropriate template data
 */
export function selectTemplate<T>(
	clinic: any,
	baseArgs: T,
	standardTemplate: (args: T) => any,
	clinicLinkTemplate: (args: T & { client_booking_URL: string }) => any
): any {
	console.log('clinic', clinic);
	console.log('baseArgs', baseArgs);
	console.log('standardTemplate', standardTemplate);
	console.log('clinicLinkTemplate', clinicLinkTemplate);

	const { isClientBookingEnabled, clientBookingUrl } =
		getClientBookingSettings(clinic);

	if (isClientBookingEnabled && clientBookingUrl) {
		return clinicLinkTemplate({
			...(baseArgs as any),
			client_booking_URL: clientBookingUrl
		});
	}

	return standardTemplate(baseArgs);
}
